#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 n8n Workflow Import & Setup${NC}"
echo "================================="

# Configuration
FORCE_IMPORT=${FORCE_IMPORT:-false}
N8N_URL="http://n8n:5678"
ADMIN_EMAIL=${N8N_ADMIN_EMAIL:-<EMAIL>}
ADMIN_PASSWORD=${N8N_ADMIN_PASSWORD:-pulse123!}

# Wait for n8n to be ready
echo "⏳ Waiting for n8n service..."
until curl -s -f "$N8N_URL/healthz" >/dev/null 2>&1; do
    echo "   n8n not ready, waiting..."
    sleep 5
done

echo -e "${GREEN}✅ n8n is ready${NC}"

# Check if this is first-time setup
echo "🔍 Checking n8n setup status..."
SETUP_STATUS=$(curl -s "$N8N_URL/api/v1/setup" | jq -r '.needsSetup // true' 2>/dev/null || echo "true")

if [ "$SETUP_STATUS" = "true" ]; then
    echo -e "${YELLOW}🔧 First-time n8n setup detected${NC}"

    # Create admin account
    echo "👤 Creating admin account..."
    SETUP_RESPONSE=$(curl -s -X POST "$N8N_URL/api/v1/setup" \
        -H "Content-Type: application/json" \
        -d "{
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$ADMIN_PASSWORD\",
            \"firstName\": \"Pulse\",
            \"lastName\": \"Admin\",
            \"agreeToLicense\": true
        }" 2>/dev/null || echo "{}")

    if echo "$SETUP_RESPONSE" | jq -e '.success // false' >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Admin account created successfully${NC}"
    else
        echo -e "${YELLOW}⚠️ Admin account may already exist${NC}"
    fi
else
    echo -e "${GREEN}✅ n8n already configured${NC}"
fi

# Get authentication token
echo "🔐 Authenticating with n8n..."
AUTH_RESPONSE=$(curl -s -X POST "$N8N_URL/api/v1/auth/login" \
    -H "Content-Type: application/json" \
    -d "{
        \"email\": \"$ADMIN_EMAIL\",
        \"password\": \"$ADMIN_PASSWORD\"
    }" 2>/dev/null || echo "{}")

TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.token // empty' 2>/dev/null || echo "")

if [ -z "$TOKEN" ]; then
    echo -e "${YELLOW}⚠️ Could not authenticate - proceeding without token${NC}"
    AUTH_HEADER=""
else
    echo -e "${GREEN}✅ Authentication successful${NC}"
    AUTH_HEADER="Authorization: Bearer $TOKEN"
fi

# Check if workflows already exist (unless force mode)
if [ "$FORCE_IMPORT" != "true" ]; then
    echo "🔍 Checking existing workflows..."
    WORKFLOW_COUNT=$(curl -s -H "$AUTH_HEADER" "$N8N_URL/api/v1/workflows" | jq '.data | length' 2>/dev/null || echo "0")

    if [ "$WORKFLOW_COUNT" -gt 0 ]; then
        echo -e "${GREEN}✅ Found $WORKFLOW_COUNT existing workflows - skipping import${NC}"
        echo "   Use FORCE_IMPORT=true to force re-import"
        exit 0
    fi
fi

echo -e "${YELLOW}📥 Importing demo workflows...${NC}"

# Import credentials if they exist
if [ -d "/seed/demo-data/credentials" ]; then
    echo "🔑 Importing credentials..."
    n8n import:credentials --separate --input=/seed/demo-data/credentials || echo "   Credentials import failed (may not exist)"
fi

# Import workflows
if [ -d "/seed/demo-data/workflows" ]; then
    echo "🔄 Importing workflows..."
    n8n import:workflow --separate --input=/seed/demo-data/workflows || echo "   Some workflows may have failed to import"

    # Activate all workflows
    echo "▶️ Activating workflows..."
    n8n update:workflow --all --active=true || echo "   Some workflows may not have been activated"
fi

# Configure webhook settings
echo "🔗 Configuring webhook settings..."
curl -s -X PATCH "$N8N_URL/api/v1/settings" \
    -H "Content-Type: application/json" \
    -H "$AUTH_HEADER" \
    -d '{
        "webhookUrl": "http://localhost:5678",
        "timezone": "UTC"
    }' >/dev/null 2>&1 || echo "   Webhook configuration may have failed"

echo -e "${GREEN}🎉 n8n workflow import complete!${NC}"
echo ""
echo "n8n Access Information:"
echo "  • URL: http://localhost:5678"
echo "  • Email: $ADMIN_EMAIL"
echo "  • Password: $ADMIN_PASSWORD"
echo ""
echo "Demo workflows imported:"
echo "  • Project Task Automation"
echo "  • Client Onboarding Automation"
echo "  • AI Project Insights"
echo ""
