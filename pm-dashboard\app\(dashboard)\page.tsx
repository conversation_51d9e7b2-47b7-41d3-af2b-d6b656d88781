"use client";

import { useAuth } from "@/contexts/AuthContext";
import { <PERSON><PERSON>, <PERSON> } from "flowbite-react";
import { HiLogo<PERSON>, HiUser } from "react-icons/hi";

export default function DashboardPage() {
  const { user, signOut, loading } = useAuth();

  const handleSignOut = async () => {
    await signOut();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Welcome to Pulse
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          AI-Powered Project Management Platform
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <div className="flex items-center space-x-4">
            <HiUser className="text-2xl text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold">User Profile</h3>
              <p className="text-sm text-gray-600">
                {user?.email || "Not logged in"}
              </p>
              <p className="text-xs text-gray-500">
                ID: {user?.id?.slice(0, 8)}...
              </p>
              <p className="text-xs text-gray-500">
                Joined: {user?.created_at ? new Date(user.created_at).toLocaleDateString() : "Unknown"}
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Projects</h3>
            <p className="text-3xl font-bold text-blue-600">0</p>
            <p className="text-sm text-gray-600">Active Projects</p>
          </div>
        </Card>

        <Card>
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Tasks</h3>
            <p className="text-3xl font-bold text-green-600">0</p>
            <p className="text-sm text-gray-600">Pending Tasks</p>
          </div>
        </Card>
      </div>

      <div className="mt-8">
        <Card>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">Authentication Status</h3>
              <p className="text-sm text-gray-600">
                You are successfully logged in to Pulse!
              </p>
            </div>
            <Button color="red" onClick={handleSignOut}>
              <HiLogout className="mr-2" />
              Sign Out
            </Button>
          </div>
        </Card>
      </div>

      <div className="mt-6">
        <Card>
          <h3 className="text-lg font-semibold mb-4">Getting Started</h3>
          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              🎉 <strong>Authentication is working!</strong> You're now connected to the Supabase backend.
            </p>
            <p className="text-sm text-gray-600">
              🚀 <strong>Next steps:</strong> Start building your project management features.
            </p>
            <p className="text-sm text-gray-600">
              🤖 <strong>AI Ready:</strong> Your backend includes n8n workflows, vector database, and AI integrations.
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
}