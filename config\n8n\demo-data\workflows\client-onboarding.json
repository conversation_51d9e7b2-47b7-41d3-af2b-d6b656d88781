{"name": "Client Onboarding Automation", "nodes": [{"parameters": {"path": "client-onboarding", "options": {}}, "id": "webhook-trigger", "name": "New Client Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "client-onboarding-webhook"}, {"parameters": {"url": "http://dashboard:3000/api/clients", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}", "Content-Type": "application/json"}}, "jsonParameters": true, "bodyParametersJson": "={\n  \"name\": \"{{$json.client_name}}\",\n  \"email\": \"{{$json.client_email}}\",\n  \"company\": \"{{$json.company}}\",\n  \"status\": \"onboarding\"\n}"}, "id": "create-client", "name": "Create Client Record", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"url": "http://dashboard:3000/api/projects/template", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}", "Content-Type": "application/json"}}, "jsonParameters": true, "bodyParametersJson": "={\n  \"client_id\": \"{{$json.id}}\",\n  \"template\": \"client_onboarding\",\n  \"name\": \"{{$json.client_name}} Onboarding\"\n}"}, "id": "create-project", "name": "Create Onboarding Project", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "url": "http://dashboard:3000/api/email/send", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}", "Content-Type": "application/json"}}, "jsonParameters": true, "bodyParametersJson": "={\n  \"to\": \"{{$json.client_email}}\",\n  \"template\": \"welcome_onboarding\",\n  \"data\": {\n    \"client_name\": \"{{$json.client_name}}\",\n    \"project_id\": \"{{$json.project_id}}\",\n    \"login_url\": \"http://localhost:3000/login\"\n  }\n}"}, "id": "send-welcome-email", "name": "Send Welcome Email", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"url": "http://dashboard:3000/api/tasks/bulk", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}", "Content-Type": "application/json"}}, "jsonParameters": true, "bodyParametersJson": "={\n  \"project_id\": \"{{$json.project_id}}\",\n  \"tasks\": [\n    {\n      \"title\": \"Initial Discovery Call\",\n      \"description\": \"Schedule and conduct initial discovery call with {{$json.client_name}}\",\n      \"priority\": \"high\",\n      \"due_date\": \"{{$now.plus({days: 2}).toISO()}}\"\n    },\n    {\n      \"title\": \"Requirements Gathering\",\n      \"description\": \"Collect detailed requirements and project scope\",\n      \"priority\": \"high\",\n      \"due_date\": \"{{$now.plus({days: 5}).toISO()}}\"\n    },\n    {\n      \"title\": \"Project Proposal\",\n      \"description\": \"Create and send project proposal\",\n      \"priority\": \"medium\",\n      \"due_date\": \"{{$now.plus({days: 7}).toISO()}}\"\n    }\n  ]\n}"}, "id": "create-tasks", "name": "Create Onboarding Tasks", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 400]}], "connections": {"New Client Webhook": {"main": [[{"node": "Create Client Record", "type": "main", "index": 0}]]}, "Create Client Record": {"main": [[{"node": "Create Onboarding Project", "type": "main", "index": 0}]]}, "Create Onboarding Project": {"main": [[{"node": "Send Welcome Email", "type": "main", "index": 0}, {"node": "Create Onboarding Tasks", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "UTC"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "client-onboarding-automation", "tags": []}