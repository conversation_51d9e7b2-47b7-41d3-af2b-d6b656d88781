#!/usr/bin/env bun

/**
 * Pulse Testing & Health Check Script
 * Modern testing using Bun for cross-platform compatibility
 * Replaces: health-check.bat, test-auth*.js, test-database-integration.bat, validate scripts
 */

import { $ } from "bun";

console.log("🧪 Pulse Health Check & Testing Suite");
console.log("=" .repeat(40));

const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

let allTestsPassed = true;
const results = [];

// Helper function to run a test
async function runTest(name, testFn) {
  console.log(`\n${colors.blue(`🔍 Testing ${name}...`)}`);
  
  try {
    const startTime = Date.now();
    await testFn();
    const duration = Date.now() - startTime;
    
    console.log(`${colors.green(`✅ ${name} - OK`)} (${duration}ms)`);
    results.push({ name, status: "PASS", duration });
  } catch (error) {
    console.log(`${colors.red(`❌ ${name} - FAILED`)}`);
    console.log(`   ${error.message}`);
    results.push({ name, status: "FAIL", error: error.message });
    allTestsPassed = false;
  }
}

// Test Docker services
async function testDockerServices() {
  const services = [
    { name: "db", port: 5432 },
    { name: "kong", port: 8000 },
    { name: "auth", port: 9999 },
    { name: "dashboard-dev", port: 3000 },
  ];
  
  for (const service of services) {
    try {
      await $`docker compose ps ${service.name} --format json`.json();
      
      // Test if service is responding on its port
      if (service.port) {
        await $`curl -f --max-time 5 http://localhost:${service.port}/health || curl -f --max-time 5 http://localhost:${service.port} || echo "Service running but no health endpoint"`.quiet();
      }
      
    } catch (error) {
      throw new Error(`Service ${service.name} is not running or not responding`);
    }
  }
}

// Test database connection and extensions
async function testDatabase() {
  // Test basic connection
  await $`docker compose exec -T db pg_isready -U postgres`;
  
  // Test PostgreSQL extensions
  const extensions = ["pgvector", "uuid-ossp"];
  
  for (const ext of extensions) {
    await $`docker compose exec -T db psql -U postgres -d postgres -c "SELECT * FROM pg_extension WHERE extname='${ext}';"`;
  }
  
  // Test if demo data exists
  try {
    const result = await $`docker compose exec -T db psql -U postgres -d postgres -t -c "SELECT COUNT(*) FROM auth.users;"`.text();
    const userCount = parseInt(result.trim());
    
    if (userCount === 0) {
      throw new Error("No users found - run 'bun seed' to add demo data");
    }
  } catch (error) {
    throw new Error(`Database query failed: ${error.message}`);
  }
}

// Test API Gateway (Kong)
async function testApiGateway() {
  // Test Kong admin endpoint
  await $`curl -f --max-time 10 http://localhost:8000/health`;
  
  // Test auth service through Kong
  await $`curl -f --max-time 10 http://localhost:8000/auth/v1/health`;
  
  // Test PostgREST through Kong
  await $`curl -f --max-time 10 http://localhost:8000/rest/v1/`;
}

// Test authentication flow
async function testAuthentication() {
  const testEmail = "<EMAIL>";
  const testPassword = "testpassword123";
  
  try {
    // Test signup
    const signupResponse = await $`curl -s -X POST http://localhost:8000/auth/v1/signup -H "Content-Type: application/json" -d '{"email":"${testEmail}","password":"${testPassword}"}'`.json();
    
    if (signupResponse.error && !signupResponse.error.message.includes("already registered")) {
      throw new Error(`Signup failed: ${signupResponse.error.message}`);
    }
    
    // Test signin
    const signinResponse = await $`curl -s -X POST http://localhost:8000/auth/v1/token?grant_type=password -H "Content-Type: application/json" -d '{"email":"${testEmail}","password":"${testPassword}"}'`.json();
    
    if (signinResponse.error) {
      throw new Error(`Signin failed: ${signinResponse.error.message}`);
    }
    
    if (!signinResponse.access_token) {
      throw new Error("No access token received");
    }
    
  } catch (error) {
    if (error.message.includes("already registered")) {
      // Test user already exists, try to sign in
      const signinResponse = await $`curl -s -X POST http://localhost:8000/auth/v1/token?grant_type=password -H "Content-Type: application/json" -d '{"email":"${testEmail}","password":"${testPassword}"}'`.json();
      
      if (!signinResponse.access_token) {
        throw new Error("Authentication flow broken");
      }
    } else {
      throw error;
    }
  }
}

// Test n8n workflows
async function testN8nWorkflows() {
  // Test n8n health
  await $`curl -f --max-time 10 http://localhost:5678/healthz`;
  
  // Test if workflows are imported
  try {
    const workflows = await $`docker compose exec -T n8n n8n list:workflow`.text();
    if (!workflows.includes("workflow")) {
      throw new Error("No workflows found - run 'bun seed' to import workflows");
    }
  } catch (error) {
    throw new Error(`Could not list workflows: ${error.message}`);
  }
}

// Test dashboard
async function testDashboard() {
  // Test if dashboard is responding
  await $`curl -f --max-time 10 http://localhost:3000/api/health`;
  
  // Test if dashboard can connect to Supabase
  const healthResponse = await $`curl -s http://localhost:3000/api/health`.json();
  
  if (healthResponse.supabase !== "ok") {
    throw new Error("Dashboard cannot connect to Supabase");
  }
}

// Display test results summary
function displayResults() {
  console.log(`\n${colors.cyan("📊 Test Results Summary:")}`);
  console.log("=" .repeat(40));
  
  let passed = 0;
  let failed = 0;
  
  for (const result of results) {
    const status = result.status === "PASS" 
      ? colors.green("✅ PASS") 
      : colors.red("❌ FAIL");
    
    const duration = result.duration ? ` (${result.duration}ms)` : "";
    console.log(`${result.name}: ${status}${duration}`);
    
    if (result.status === "PASS") passed++;
    else failed++;
  }
  
  console.log("=" .repeat(40));
  console.log(`Total: ${passed + failed}, Passed: ${colors.green(passed)}, Failed: ${colors.red(failed)}`);
  
  if (allTestsPassed) {
    console.log(`\n${colors.green("🎉 All tests passed! Your Pulse environment is healthy.")}`);
  } else {
    console.log(`\n${colors.red("❌ Some tests failed. Check the output above for details.")}`);
    console.log(`\nTroubleshooting tips:`);
    console.log(`• Make sure all services are running: ${colors.cyan("bun dev")}`);
    console.log(`• Seed demo data if missing: ${colors.cyan("bun seed")}`);
    console.log(`• Reset environment if issues persist: ${colors.cyan("bun reset")}`);
  }
}

// Main testing flow
async function main() {
  console.log(`${colors.blue("Starting comprehensive health checks...")}\n`);
  
  await runTest("Docker Services", testDockerServices);
  await runTest("Database Connection", testDatabase);
  await runTest("API Gateway", testApiGateway);
  await runTest("Authentication", testAuthentication);
  await runTest("n8n Workflows", testN8nWorkflows);
  await runTest("Dashboard", testDashboard);
  
  displayResults();
  
  if (!allTestsPassed) {
    process.exit(1);
  }
}

main();