"use client";

import { useSidebarContext } from "@/contexts/sidebar-context";
import { useAuth } from "@/contexts/AuthContext";
import type { PropsWithChildren } from "react";
import { twMerge } from "tailwind-merge";

export function LayoutContent({ children }: PropsWithChildren) {
  const sidebar = useSidebarContext();
  const { user } = useAuth();

  return (
    <div
      id="main-content"
      className={twMerge(
        "relative h-full w-full overflow-y-auto bg-gray-50 dark:bg-gray-900",
        sidebar.desktop.isCollapsed ? "lg:ml-16" : "lg:ml-64",
      )}
    >
      {/* Optional: Add user context to the layout */}
      <div className="hidden" data-user-id={user?.id} data-user-email={user?.email}>
        {/* This div provides user context for debugging/analytics */}
      </div>
      {children}
    </div>
  );
}
