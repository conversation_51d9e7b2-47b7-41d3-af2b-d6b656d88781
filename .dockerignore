# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Build outputs
dist/
build/
out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Scripts (keep them out of build context unless needed)
scripts/

# Volumes directory (contains runtime data)
volumes/

# Config directory (may contain sensitive data)
config/

# Temporary folders
tmp/
temp/

# Test files
__tests__/
**/*.test.*
**/*.spec.*

# Linting and formatting
.eslintrc*
.prettierrc*
.editorconfig

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml
bun.lockb

# TypeScript
tsconfig.json

# Next.js
.next/
.nuxt/

# Cache directories
.cache/
.parcel-cache/

# Storybook
.storybook-out/
.out/

# Misc
*.tgz
*.tar.gz
