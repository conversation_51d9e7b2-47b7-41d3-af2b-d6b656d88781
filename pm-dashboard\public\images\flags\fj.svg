<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="20" rx="2" fill="white"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect width="28" height="20" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0)">
<rect width="28" height="20" fill="#79CFF6"/>
<rect width="12" height="9.33333" fill="url(#paint0_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.3333 10.0002C17.3333 8.00016 17.3333 5.3335 17.3333 5.3335H23.9999C23.9999 5.3335 23.9999 8.00016 23.9999 10.0002C23.9999 13.3335 20.6666 14.6668 20.6666 14.6668C20.6666 14.6668 17.3333 13.3335 17.3333 10.0002Z" fill="white"/>
<mask id="mask1" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="17" y="5" width="7" height="10">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.3333 10.0002C17.3333 8.00016 17.3333 5.3335 17.3333 5.3335H23.9999C23.9999 5.3335 23.9999 8.00016 23.9999 10.0002C23.9999 13.3335 20.6666 14.6668 20.6666 14.6668C20.6666 14.6668 17.3333 13.3335 17.3333 10.0002Z" fill="white"/>
</mask>
<g mask="url(#mask1)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.3333 8.00016C19.3333 8.36835 19.0349 8.66683 18.6667 8.66683C18.2985 8.66683 18 8.36835 18 8.00016C18 7.63197 18.2985 7.3335 18.6667 7.3335C19.0349 7.3335 19.3333 7.63197 19.3333 8.00016ZM23.3333 8.00016C23.3333 8.36835 23.0349 8.66683 22.6667 8.66683C22.2985 8.66683 22 8.36835 22 8.00016C22 7.63197 22.2985 7.3335 22.6667 7.3335C23.0349 7.3335 23.3333 7.63197 23.3333 8.00016Z" fill="#2A915C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.3333 5.3335H23.9999V6.66683H21.3333V9.3335H23.9999V10.6668H21.3333V14.6668H19.9999V10.6668H17.3333V9.3335H19.9999V6.66683H17.3333V5.3335Z" fill="#EB1D43"/>
</g>
<path d="M0 -0.333333H-0.901086L-0.21693 0.253086L4.33333 4.15331V5.16179L-0.193746 8.39542L-0.333333 8.49513V8.66667V9.33333V9.93475L0.176666 9.616L5.42893 6.33333H6.55984L11.0821 9.56351C11.176 9.6306 11.2886 9.66667 11.404 9.66667C11.9182 9.66667 12.1548 9.02698 11.7644 8.69237L7.66667 5.18002V4.17154L12.0542 1.03762C12.2294 0.912475 12.3333 0.710428 12.3333 0.495127V0V-0.601416L11.8233 -0.282666L6.57107 3H5.44016L0.860413 -0.271244L0.773488 -0.333333H0.666667H0Z" fill="#DB1E36" stroke="white" stroke-width="0.666667"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 3.33333V6H4.66667V9.33333C4.66667 9.70152 4.96514 10 5.33333 10H6.66667C7.03486 10 7.33333 9.70152 7.33333 9.33333V6H12C12.3682 6 12.6667 5.70152 12.6667 5.33333V4C12.6667 3.63181 12.3682 3.33333 12 3.33333H7.33333V0H4.66667V3.33333H0Z" fill="url(#paint1_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 4H5.33333V3.33333V0H6.66667V3.33333V4H12V5.33333H6.66667V6V9.33333H5.33333V6V5.33333H0V4Z" fill="#DB1E36"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="0" y1="0" x2="0" y2="9.33333" gradientUnits="userSpaceOnUse">
<stop stop-color="#042C90"/>
<stop offset="1" stop-color="#00247E"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="0" y1="0" x2="0" y2="10" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F0F0F0"/>
</linearGradient>
</defs>
</svg>
