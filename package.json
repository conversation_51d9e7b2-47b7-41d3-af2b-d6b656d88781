{"name": "pulse-project-management", "version": "1.0.0", "description": "AI-powered project management platform with intelligent automation", "scripts": {"setup": "bun run scripts/setup.js", "dev": "bun run scripts/dev.js", "seed": "bun run scripts/seed.js", "test": "bun run scripts/test.js", "reset": "bun run scripts/reset.js", "build": "cd pm-dashboard && bun run build", "lint": "cd pm-dashboard && bun run lint", "typecheck": "cd pm-dashboard && bun run typecheck", "logs": "docker compose logs -f", "db:connect": "docker exec -it pulse-db psql -U postgres", "prod": "docker compose --profile prod up -d --build"}, "engines": {"node": ">=18.0.0", "docker": ">=20.0.0"}}