#!/usr/bin/env bun

/**
 * Pulse Reset Script
 * Modern reset using Bun for cross-platform compatibility
 * Replaces: reset scripts, show-what-will-be-deleted.bat, selective reset functionality
 */

import { $ } from "bun";
import { readFileSync } from "fs";

console.log("🔄 Pulse Environment Reset");
console.log("=" .repeat(30));

const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
  magenta: (text) => `\x1b[35m${text}\x1b[0m`,
};

// Parse command line arguments
const args = process.argv.slice(2);
const scope = args.find(arg => arg.startsWith('--scope='))?.split('=')[1] || 'all';
const force = args.includes('--force');
const preserveData = args.includes('--preserve-data');

// Reset scope options
const SCOPES = {
  all: "Complete reset (containers, volumes, networks)",
  services: "Stop and remove containers only",
  database: "Reset database data only", 
  dashboard: "Reset dashboard container only",
  n8n: "Reset n8n workflows and data only",
  volumes: "Remove all persistent volumes"
};

// Show what will be reset
function showResetPlan() {
  console.log(`${colors.cyan("🎯 Reset Scope:")} ${colors.yellow(scope)}`);
  console.log(`${colors.cyan("📋 Description:")} ${SCOPES[scope] || "Custom scope"}`);
  
  console.log(`\n${colors.yellow("⚠️  The following will be affected:")}`);
  
  switch (scope) {
    case 'all':
      console.log("• All Docker containers will be stopped and removed");
      console.log("• All persistent volumes will be removed");
      console.log("• All networks will be removed");
      console.log("• Database data will be lost");
      console.log("• n8n workflows will be lost");
      console.log("• Dashboard build cache will be cleared");
      break;
      
    case 'services':
      console.log("• All Docker containers will be stopped and removed");
      console.log("• Persistent data will be preserved");
      break;
      
    case 'database':
      console.log("• Database container will be reset");
      console.log("• All database data will be lost");
      console.log("• Demo users and projects will be removed");
      break;
      
    case 'dashboard':
      console.log("• Dashboard container will be reset");
      console.log("• Build cache will be cleared");
      break;
      
    case 'n8n':
      console.log("• n8n container will be reset");
      console.log("• All workflows will be lost");
      console.log("• n8n data will be cleared");
      break;
      
    case 'volumes':
      console.log("• All persistent volumes will be removed");
      console.log("• Database data will be lost");
      console.log("• n8n data will be lost");
      console.log("• Storage data will be lost");
      break;
  }
  
  if (preserveData) {
    console.log(`\n${colors.green("✅ Data preservation enabled - volumes will be kept")}`);
  }
}

// Confirm reset action
async function confirmReset() {
  if (force) {
    console.log(`\n${colors.yellow("🚀 Force mode enabled, proceeding without confirmation...")}`);
    return true;
  }
  
  console.log(`\n${colors.red("❗ This action cannot be undone!")}`);
  console.log(`${colors.cyan("Type 'yes' to confirm, or 'no' to cancel:")}`);
  
  const response = await new Promise(resolve => {
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    process.stdin.once('data', data => {
      resolve(data.toString().trim().toLowerCase());
    });
  });
  
  return response === 'yes' || response === 'y';
}

// Get current service status
async function getServiceStatus() {
  try {
    const runningServices = await $`docker compose ps --services --filter "status=running"`.text();
    const allServices = await $`docker compose ps --services`.text();
    
    return {
      running: runningServices.trim().split('\n').filter(s => s.trim()),
      all: allServices.trim().split('\n').filter(s => s.trim())
    };
  } catch (error) {
    return { running: [], all: [] };
  }
}

// Stop services
async function stopServices() {
  console.log(`${colors.blue("🛑 Stopping services...")}`);
  
  try {
    await $`docker compose down`;
    console.log(`${colors.green("✅ Services stopped")}`);
  } catch (error) {
    console.log(`${colors.yellow("⚠️  Some services may not have been running")}`);
  }
}

// Remove volumes
async function removeVolumes() {
  if (preserveData) {
    console.log(`${colors.yellow("⏭️  Skipping volume removal (data preservation enabled)")}`);
    return;
  }
  
  console.log(`${colors.blue("🗑️  Removing persistent volumes...")}`);
  
  try {
    await $`docker compose down -v`;
    console.log(`${colors.green("✅ Volumes removed")}`);
  } catch (error) {
    console.log(`${colors.yellow("⚠️  Some volumes may not exist")}`);
  }
}

// Selective service reset
async function resetSpecificServices(services) {
  console.log(`${colors.blue(`🔄 Resetting specific services: ${services.join(', ')}`)}`);
  
  for (const service of services) {
    try {
      await $`docker compose stop ${service}`;
      await $`docker compose rm -f ${service}`;
      console.log(`   ${colors.green(`✅ ${service} reset`)}`);
    } catch (error) {
      console.log(`   ${colors.yellow(`⚠️  ${service} may not exist`)}`);
    }
  }
}

// Clean Docker system
async function cleanDockerSystem() {
  console.log(`${colors.blue("🧹 Cleaning Docker system...")}`);
  
  try {
    // Remove unused networks
    await $`docker network prune -f`;
    
    // Remove unused images (only for this project)
    await $`docker image prune -f --filter "label=com.docker.compose.project=pulse-project-management"`;
    
    console.log(`${colors.green("✅ Docker system cleaned")}`);
  } catch (error) {
    console.log(`${colors.yellow("⚠️  Docker cleanup partially completed")}`);
  }
}

// Show reset completion info
function showCompletionInfo() {
  console.log(`\n${colors.green("🎉 Reset completed successfully!")}`);
  
  console.log(`\n${colors.cyan("Next steps:")}`);
  console.log(`• Start fresh environment: ${colors.magenta("bun dev")}`);
  console.log(`• Add demo data: ${colors.magenta("bun seed")}`);
  console.log(`• Check system health: ${colors.magenta("bun test")}`);
  
  console.log(`\n${colors.yellow("💡 Tip: Use --preserve-data flag to keep volumes during reset")}`);
}

// Main reset flow
async function main() {
  // Validate scope
  if (!SCOPES[scope] && scope !== 'all') {
    console.log(`${colors.red("❌ Invalid scope:")} ${scope}`);
    console.log(`${colors.cyan("Available scopes:")} ${Object.keys(SCOPES).join(', ')}`);
    process.exit(1);
  }
  
  showResetPlan();
  
  const confirmed = await confirmReset();
  if (!confirmed) {
    console.log(`${colors.yellow("👋 Reset cancelled")}`);
    process.exit(0);
  }
  
  console.log(`\n${colors.blue("🔄 Starting reset process...")}\n`);
  
  try {
    const status = await getServiceStatus();
    
    switch (scope) {
      case 'all':
        await stopServices();
        await removeVolumes();
        await cleanDockerSystem();
        break;
        
      case 'services':
        await stopServices();
        break;
        
      case 'database':
        await resetSpecificServices(['db']);
        if (!preserveData) {
          await $`docker volume rm pulse-project-management_db-config || true`;
        }
        break;
        
      case 'dashboard':
        await resetSpecificServices(['dashboard-dev', 'dashboard']);
        break;
        
      case 'n8n':
        await resetSpecificServices(['n8n', 'n8n-import']);
        if (!preserveData) {
          await $`docker volume rm pulse-project-management_volumes_n8n || true`;
        }
        break;
        
      case 'volumes':
        await stopServices();
        await removeVolumes();
        break;
        
      default:
        await stopServices();
        break;
    }
    
    showCompletionInfo();
    
  } catch (error) {
    console.log(`\n${colors.red("❌ Reset failed:")}`);
    console.log(error.message);
    process.exit(1);
  }
}

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
  console.log(`\n${colors.cyan("Usage:")} bun reset [options]`);
  console.log(`\n${colors.cyan("Options:")}`);
  console.log(`  --scope=<scope>     Reset scope (default: all)`);
  console.log(`  --force             Skip confirmation prompt`);
  console.log(`  --preserve-data     Keep persistent volumes`);
  console.log(`  --help, -h          Show this help`);
  console.log(`\n${colors.cyan("Available scopes:")}`);
  Object.entries(SCOPES).forEach(([key, desc]) => {
    console.log(`  ${colors.yellow(key.padEnd(12))} ${desc}`);
  });
  console.log(`\n${colors.cyan("Examples:")}`);
  console.log(`  bun reset                          # Full reset with confirmation`);
  console.log(`  bun reset --force                  # Full reset without confirmation`);
  console.log(`  bun reset --scope=database         # Reset only database`);
  console.log(`  bun reset --scope=all --preserve-data  # Reset but keep data`);
  process.exit(0);
}

main();