#!/usr/bin/env bun

/**
 * Pulse Development Script
 * Modern development startup using Bun
 * Replaces: npm run dev, npm run dev:watch, npm run init, docker-init.sh
 */

import { $ } from "bun";
import { existsSync } from "fs";

console.log("🚀 Starting Pulse Development Environment");
console.log("=" .repeat(45));

const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
  magenta: (text) => `\x1b[35m${text}\x1b[0m`,
};

// Check if first time setup is needed
async function checkFirstTimeSetup() {
  if (!existsSync(".env")) {
    console.log(`${colors.yellow("⚠️  Environment not configured. Running setup first...")}\n`);
    await $`bun run scripts/setup.js`;
    console.log();
  }
}

// Check if services are already running
async function checkExistingServices() {
  try {
    const result = await $`docker compose ps --services --filter "status=running"`.text();
    if (result.trim()) {
      console.log(`${colors.yellow("⚠️  Some services are already running:")}`);
      console.log(result);
      console.log(`${colors.cyan("Stopping existing services...")}\n`);
      await $`docker compose down`;
    }
  } catch (error) {
    // Services not running, continue
  }
}

// Initialize database if needed
async function initializeDatabase() {
  console.log(`${colors.blue("🗄️  Initializing database...")}`);
  
  try {
    // Check if database is seeded by looking for demo users
    await $`docker compose up -d db`.quiet();
    
    // Wait for database to be ready
    console.log("Waiting for database to be ready...");
    let retries = 30;
    while (retries > 0) {
      try {
        await $`docker compose exec db pg_isready -U postgres`.quiet();
        break;
      } catch {
        retries--;
        if (retries === 0) {
          throw new Error("Database failed to start");
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`${colors.green("✅ Database ready")}`);
  } catch (error) {
    console.log(`${colors.red("❌ Database initialization failed:")}`);
    console.log(error.message);
    process.exit(1);
  }
}

// Start development services
async function startDevelopment() {
  console.log(`\n${colors.blue("🐳 Starting development services...")}\n`);
  
  // Start core services first
  console.log("Starting core infrastructure...");
  await $`docker compose up -d db vector analytics`;
  
  // Wait a moment for core services
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Start API services
  console.log("Starting API services...");
  await $`docker compose up -d auth rest realtime storage functions meta`;
  
  // Wait for API services
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // Start API gateway
  console.log("Starting API gateway...");
  await $`docker compose up -d kong`;
  
  // Start n8n workflow platform
  console.log("Starting n8n automation...");
  await $`docker compose up -d n8n-import n8n`;
  
  // Start development dashboard with watch mode
  console.log("Starting dashboard with hot-reload...");
  process.env.DOCKER_ENV = "true"; // Enable file watching in Docker
  await $`docker compose up -d dashboard-dev`;
  
  console.log(`\n${colors.green("🎉 Development environment started!")}`);
}

// Display service information
async function showServiceInfo() {
  console.log(`\n${colors.cyan("📍 Service URLs:")}`);
  console.log(`• Dashboard:     ${colors.blue("http://localhost:3000")}`);
  console.log(`• API Gateway:   ${colors.blue("http://localhost:8000")}`);
  console.log(`• n8n Workflows: ${colors.blue("http://localhost:5678")}`);
  console.log(`• Email Testing: ${colors.blue("http://localhost:9000")}`);
  
  console.log(`\n${colors.cyan("🔧 Management Commands:")}`);
  console.log(`• View logs:     ${colors.magenta("bun logs")}`);
  console.log(`• Add demo data: ${colors.magenta("bun seed")}`);
  console.log(`• Run tests:     ${colors.magenta("bun test")}`);
  console.log(`• Clean reset:   ${colors.magenta("bun reset")}`);
  
  console.log(`\n${colors.yellow("💡 Tip: Dashboard has hot-reload enabled for development")}`);
}

// Monitor services and show logs
async function showLogs() {
  console.log(`\n${colors.cyan("📊 Starting log monitoring...")}`);
  console.log(`${colors.yellow("Press Ctrl+C to stop")}\n`);
  
  // Show logs from key services
  try {
    await $`docker compose logs -f dashboard-dev kong auth n8n`;
  } catch (error) {
    // User interrupted
    console.log(`\n${colors.yellow("👋 Logs stopped")}`);
  }
}

// Main development flow
async function main() {
  try {
    await checkFirstTimeSetup();
    await checkExistingServices();
    await initializeDatabase();
    await startDevelopment();
    await showServiceInfo();
    
    // Start log monitoring
    await showLogs();
    
  } catch (error) {
    console.log(`\n${colors.red("❌ Development startup failed:")}`);
    console.log(error.message);
    console.log(`\nTry running: ${colors.cyan("bun reset")} and then ${colors.cyan("bun dev")} again`);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log(`\n${colors.yellow("🛑 Shutting down development environment...")}`);
  try {
    await $`docker compose down`;
    console.log(`${colors.green("✅ Services stopped")}`);
  } catch (error) {
    console.log(`${colors.red("❌ Error stopping services:")}`);
    console.log(error.message);
  }
  process.exit(0);
});

main();