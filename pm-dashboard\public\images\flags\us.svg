<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="20" rx="2" fill="white"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect width="28" height="20" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28 0H0V1.33333H28V0ZM28 2.66667H0V4H28V2.66667ZM0 5.33333H28V6.66667H0V5.33333ZM28 8H0V9.33333H28V8ZM0 10.6667H28V12H0V10.6667ZM28 13.3333H0V14.6667H28V13.3333ZM0 16H28V17.3333H0V16ZM28 18.6667H0V20H28V18.6667Z" fill="#D02F44"/>
<rect width="12" height="9.33333" fill="#46467F"/>
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.66659 1.99992C2.66659 2.36811 2.36811 2.66659 1.99992 2.66659C1.63173 2.66659 1.33325 2.36811 1.33325 1.99992C1.33325 1.63173 1.63173 1.33325 1.99992 1.33325C2.36811 1.33325 2.66659 1.63173 2.66659 1.99992ZM5.33325 1.99992C5.33325 2.36811 5.03478 2.66659 4.66659 2.66659C4.2984 2.66659 3.99992 2.36811 3.99992 1.99992C3.99992 1.63173 4.2984 1.33325 4.66659 1.33325C5.03478 1.33325 5.33325 1.63173 5.33325 1.99992ZM7.33325 2.66659C7.70144 2.66659 7.99992 2.36811 7.99992 1.99992C7.99992 1.63173 7.70144 1.33325 7.33325 1.33325C6.96506 1.33325 6.66659 1.63173 6.66659 1.99992C6.66659 2.36811 6.96506 2.66659 7.33325 2.66659ZM10.6666 1.99992C10.6666 2.36811 10.3681 2.66659 9.99992 2.66659C9.63173 2.66659 9.33325 2.36811 9.33325 1.99992C9.33325 1.63173 9.63173 1.33325 9.99992 1.33325C10.3681 1.33325 10.6666 1.63173 10.6666 1.99992ZM3.33325 3.99992C3.70144 3.99992 3.99992 3.70144 3.99992 3.33325C3.99992 2.96506 3.70144 2.66659 3.33325 2.66659C2.96506 2.66659 2.66659 2.96506 2.66659 3.33325C2.66659 3.70144 2.96506 3.99992 3.33325 3.99992ZM6.66659 3.33325C6.66659 3.70144 6.36811 3.99992 5.99992 3.99992C5.63173 3.99992 5.33325 3.70144 5.33325 3.33325C5.33325 2.96506 5.63173 2.66659 5.99992 2.66659C6.36811 2.66659 6.66659 2.96506 6.66659 3.33325ZM8.66658 3.99992C9.03477 3.99992 9.33325 3.70144 9.33325 3.33325C9.33325 2.96506 9.03477 2.66659 8.66658 2.66659C8.2984 2.66659 7.99992 2.96506 7.99992 3.33325C7.99992 3.70144 8.2984 3.99992 8.66658 3.99992ZM10.6666 4.66659C10.6666 5.03478 10.3681 5.33325 9.99992 5.33325C9.63173 5.33325 9.33325 5.03478 9.33325 4.66659C9.33325 4.2984 9.63173 3.99992 9.99992 3.99992C10.3681 3.99992 10.6666 4.2984 10.6666 4.66659ZM7.33325 5.33325C7.70144 5.33325 7.99992 5.03478 7.99992 4.66659C7.99992 4.2984 7.70144 3.99992 7.33325 3.99992C6.96506 3.99992 6.66659 4.2984 6.66659 4.66659C6.66659 5.03478 6.96506 5.33325 7.33325 5.33325ZM5.33325 4.66659C5.33325 5.03478 5.03478 5.33325 4.66659 5.33325C4.2984 5.33325 3.99992 5.03478 3.99992 4.66659C3.99992 4.2984 4.2984 3.99992 4.66659 3.99992C5.03478 3.99992 5.33325 4.2984 5.33325 4.66659ZM1.99992 5.33325C2.36811 5.33325 2.66659 5.03478 2.66659 4.66659C2.66659 4.2984 2.36811 3.99992 1.99992 3.99992C1.63173 3.99992 1.33325 4.2984 1.33325 4.66659C1.33325 5.03478 1.63173 5.33325 1.99992 5.33325ZM3.99992 5.99992C3.99992 6.36811 3.70144 6.66659 3.33325 6.66659C2.96506 6.66659 2.66659 6.36811 2.66659 5.99992C2.66659 5.63173 2.96506 5.33325 3.33325 5.33325C3.70144 5.33325 3.99992 5.63173 3.99992 5.99992ZM5.99992 6.66659C6.36811 6.66659 6.66659 6.36811 6.66659 5.99992C6.66659 5.63173 6.36811 5.33325 5.99992 5.33325C5.63173 5.33325 5.33325 5.63173 5.33325 5.99992C5.33325 6.36811 5.63173 6.66659 5.99992 6.66659ZM9.33325 5.99992C9.33325 6.36811 9.03477 6.66659 8.66658 6.66659C8.2984 6.66659 7.99992 6.36811 7.99992 5.99992C7.99992 5.63173 8.2984 5.33325 8.66658 5.33325C9.03477 5.33325 9.33325 5.63173 9.33325 5.99992ZM9.99992 7.99992C10.3681 7.99992 10.6666 7.70144 10.6666 7.33325C10.6666 6.96506 10.3681 6.66659 9.99992 6.66659C9.63173 6.66659 9.33325 6.96506 9.33325 7.33325C9.33325 7.70144 9.63173 7.99992 9.99992 7.99992ZM7.99992 7.33325C7.99992 7.70144 7.70144 7.99992 7.33325 7.99992C6.96506 7.99992 6.66659 7.70144 6.66659 7.33325C6.66659 6.96506 6.96506 6.66659 7.33325 6.66659C7.70144 6.66659 7.99992 6.96506 7.99992 7.33325ZM4.66659 7.99992C5.03478 7.99992 5.33325 7.70144 5.33325 7.33325C5.33325 6.96506 5.03478 6.66659 4.66659 6.66659C4.2984 6.66659 3.99992 6.96506 3.99992 7.33325C3.99992 7.70144 4.2984 7.99992 4.66659 7.99992ZM2.66659 7.33325C2.66659 7.70144 2.36811 7.99992 1.99992 7.99992C1.63173 7.99992 1.33325 7.70144 1.33325 7.33325C1.33325 6.96506 1.63173 6.66659 1.99992 6.66659C2.36811 6.66659 2.66659 6.96506 2.66659 7.33325Z" fill="url(#paint0_linear)"/>
</g>
</g>
<defs>
<filter id="filter0_d" x="1.33325" y="1.33325" width="9.33333" height="7.66667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="1.33325" y1="1.33325" x2="1.33325" y2="7.99992" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F0F0F0"/>
</linearGradient>
</defs>
</svg>
