import withFlowbiteReact from "flowbite-react/plugin/nextjs";

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Always use standalone output for optimal Docker builds
  output: 'standalone',

  // Enable React Strict Mode for better development experience
  reactStrictMode: true,

  // Optimize production builds
  eslint: {
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },

  // Turbopack configuration (stable in Next.js 15.3+)
  turbopack: {
    // Modern Turbopack configuration
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // Modern experimental features for Next.js 15.3.3
  experimental: {
    optimizePackageImports: ['flowbite-react', '@supabase/supabase-js'],
  },

  // Optimized for Docker development with file watching
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer && process.env.DOCKER_ENV) {
      // Enable efficient file watching in Docker
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
        ignored: /node_modules/,
      };
    }
    return config;
  },
};

export default withFlowbiteReact(nextConfig);
