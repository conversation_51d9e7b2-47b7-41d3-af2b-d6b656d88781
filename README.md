# 🚀 Pulse - AI-Powered Project Management Platform

> **Intelligent project management with AI automation. Ship features faster than ever.**

An open-source, AI-enhanced project management platform that combines professional dashboard components with intelligent automation. Built for teams who want to leverage AI for better project outcomes and streamlined workflows.

**✨ Windows-Optimized** - All scripts and commands are designed for Windows development environments.

![Supabase](https://img.shields.io/badge/supabase-3ECF8E?style=for-the-badge&logo=supabase&logoColor=white)
![Next.js](https://img.shields.io/badge/next.js-000000?style=for-the-badge&logo=nextdotjs&logoColor=white)
![Flowbite](https://img.shields.io/badge/flowbite-06B6D4?style=for-the-badge&logo=tailwindcss&logoColor=white)
![n8n](https://img.shields.io/badge/n8n-EA4B71?style=for-the-badge&logo=n8n&logoColor=white)
![Kong](https://img.shields.io/badge/kong-003459?style=for-the-badge&logo=kong&logoColor=white)
![Docker](https://img.shields.io/badge/docker-2496ED?style=for-the-badge&logo=docker&logoColor=white)
![PostgreSQL](https://img.shields.io/badge/postgresql-336791?style=for-the-badge&logo=postgresql&logoColor=white)
![OpenRouter](https://img.shields.io/badge/openrouter-FF6B6B?style=for-the-badge&logo=openai&logoColor=white)

**AI-Enhanced Project Management. Professional UI. Complete Docker Stack.**

## 🚀 What You Get

### 🎯 **AI-Powered Project Management**

- **AI Chat Assistant** - Get help with projects, tasks, and workflows using multiple LLM providers
- **Vector Search** - Find information across projects using natural language queries
- **Workflow Automation** - Visual n8n workflows for intelligent process automation
- **Real-time Analytics** - AI-powered insights and predictive project metrics
- **Smart Notifications** - Automated alerts and recommendations based on project patterns

### 🎨 **Professional Dashboard**

- **Flowbite UI Components** - Beautiful, responsive dashboard with professional design
- **Next.js 14 App Router** - Modern React framework with server-side rendering
- **Tailwind CSS** - Utility-first CSS framework for rapid development
- **Mobile-First Design** - Fully responsive interface for all devices
- **Dark Mode Support** - Complete light/dark theme switching

### 🔧 **Complete Backend Infrastructure**

- **PostgreSQL + pgvector** - Vector database for embeddings and semantic search
- **Supabase Auth** - Email/password authentication with email confirmation
- **Kong API Gateway** - Professional API management and routing
- **Edge Functions** - Serverless TypeScript functions for AI processing
- **n8n Workflows** - Visual automation for AI pipelines and integrations
- **Docker Compose** - Complete containerized development environment

## 🎯 Perfect For

### 🏢 **Development Teams**

- **Agile Project Management** - Sprint planning, backlog management, task tracking
- **AI-Enhanced Workflows** - Automated code reviews, deployment workflows, testing
- **Knowledge Management** - Searchable documentation, team knowledge base
- **Real-time Collaboration** - Team chat, project updates, notifications

### 🚀 **Startup & Scale-ups**

- **Product Development** - Feature planning, user story management, roadmaps
- **Growth Analytics** - AI-powered insights, user behavior analysis
- **Team Scaling** - Onboarding workflows, process automation
- **Resource Management** - Budget tracking, time management, capacity planning

### 🏭 **Enterprise Organizations**

- **Portfolio Management** - Multi-project oversight, resource allocation
- **Compliance & Reporting** - Automated compliance checks, audit trails
- **Integration Capabilities** - Connect with existing tools and systems
- **Security & Governance** - Role-based access, data protection, audit logs

## 🚀 Quick Start - Windows

### **1. Clone & Setup**

```cmd
git clone <your-repo-url>
cd pulse-project-management

REM Copy environment template and configure
copy .env.example .env
```

### **2. Start Development (One Command!)**

```cmd
REM For development with hot reload
npm run dev

REM OR for production-like mode
npm run start
```

**That's it!** Both commands will:
- ✅ Start all services (database, dashboard, API, email testing)
- ✅ **Automatically detect** if database needs seeding
- ✅ **Auto-create** demo users and sample projects (first run only)
- ✅ **Skip seeding** on subsequent runs (smart detection)

### **3. Access Your Application**

- **Application**: http://localhost:3000
- **n8n Workflows**: http://localhost:5678
- **Email Testing**: http://localhost:9000 (Inbucket)
- **API Gateway**: http://localhost:8000

### **4. Demo Login Credentials**

- **<EMAIL>** (password: demo123!)
- **<EMAIL>** (password: demo123!)
- **<EMAIL>** (password: demo123!)

### **5. Smart Features**

- 🧠 **Smart Seeding**: Only seeds database on first run
- 🔥 **Hot Reload**: `npm run dev` includes live code reloading
- 🔍 **Auto-Detection**: Checks if demo data exists before seeding
- ⚡ **Fast Startup**: Subsequent runs skip seeding for faster startup

**You now have a complete project management platform with intelligent setup!**

## 🌱 Initialize Demo Data

To get started with sample data and users:

```cmd
npm run seed
```

This will create:
- **Demo users** with login credentials
- **Sample workspace** with projects and tasks
- **n8n workflows** for automation
- **Complete project structure** ready for development

**Demo Users (password: `demo123!`):**
- `<EMAIL>` - Administrator
- `<EMAIL>` - Project Manager
- `<EMAIL>` - Developer

> 💡 **Pro Tip:** All scripts are Windows batch files (.bat) optimized for Windows development environments. Use `npm run` to see all available commands.

## 🌐 Service Architecture

| Service              | URL                                     | Purpose                                 |
| -------------------- | --------------------------------------- | --------------------------------------- |
| **Frontend Dashboard** | [localhost:3000](http://localhost:3000) | Main application interface            |
| **Kong API Gateway** | [localhost:8000](http://localhost:8000) | API entry point, routing, security     |
| **n8n Workflows**    | [localhost:5678](http://localhost:5678) | Visual AI workflow automation           |
| **Email Testing**    | [localhost:9000](http://localhost:9000) | Development email server (dev mode)     |
| **Supabase Auth**    | localhost:8000/auth/v1/\*               | Authentication endpoints via Kong       |
| **PostgREST API**    | localhost:8000/rest/v1/\*               | Database REST API via Kong              |
| **Edge Functions**   | localhost:8000/functions/v1/\*          | AI services (chat, search, webhooks)   |
| **Realtime**         | localhost:8000/realtime/v1/\*           | WebSocket connections via Kong          |

### 🏗️ Core Infrastructure

```
┌─ Frontend (3000) ────────────────────────────────────┐
│                                                      │
├─ Next.js 14 App Router                              │
├─ Flowbite React Components                          │
├─ AI Chat Interface                                  │
├─ Vector Search                                      │
└─ Workflow Management                                │

┌─ Kong API Gateway (8000) ────────────────────────────┐
│                                                      │
├─ Auth Service (/auth/v1/*)                          │
├─ REST API (/rest/v1/*)                              │
├─ Realtime (/realtime/v1/*)                          │
├─ Storage (/storage/v1/*)                            │
└─ Edge Functions (/functions/v1/*)                   │

┌─ AI & Automation ───────────────────────────────────┐
│                                                      │
├─ n8n Workflows (5678)                               │
├─ PostgreSQL + pgvector                              │
├─ OpenRouter AI Models                               │
└─ Vector Embeddings                                  │
```

## 🧪 Built-in Testing & Validation

### **Complete Test Suite**

- **Postman Collections** - All API endpoints with environment variables
- **Authentication Tests** - Full signup/signin/confirmation flow
- **Health Checks** - Service monitoring and connectivity validation
- **Integration Tests** - End-to-end workflow validation

### **Test Authentication Flow**

```cmd
REM Test complete auth flow with email confirmation
node scripts/test-auth-complete.js

REM Test direct auth service (bypass Kong)
node scripts/test-auth-direct.js

REM Basic auth functionality
node scripts/test-auth.js
```

### **API Testing with Postman**

```cmd
REM Import collections (pre-configured)
REM 1. Open Postman
REM 2. Import: postman/Pulse-Project-Management.postman_collection.json
REM 3. Import: postman/Pulse-Project-Management.postman_environment.json
REM 4. Configure environment variables with your keys

REM Or setup keys automatically
cd postman && setup-keys.bat
```

## 🧠 AI Development Patterns

### **Vector Search & RAG**

```sql
-- PostgreSQL with pgvector is ready for AI embeddings
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  content TEXT,
  embedding VECTOR(1536),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Semantic search example
SELECT content, 1 - (embedding <=> '[0.1,0.2,...]'::vector) as similarity
FROM documents
ORDER BY embedding <=> '[0.1,0.2,...]'::vector
LIMIT 5;
```

### **n8n AI Workflows**

- **Pre-configured integrations** for OpenAI, Anthropic, Hugging Face
- **Vector database nodes** for embeddings and similarity search
- **Webhook endpoints** for real-time AI processing
- **Background jobs** for batch AI operations
- **Template workflows** for common AI patterns

### **Real-time AI Features**

```javascript
// WebSocket connection for streaming AI responses
import { createClient } from '@supabase/supabase-js';

const supabase = createClient('http://localhost:8000', 'your-anon-key');

// Subscribe to real-time AI updates
supabase
  .channel('ai-responses')
  .on(
    'postgres_changes',
    { event: 'INSERT', schema: 'public', table: 'ai_responses' },
    (payload) => console.log('New AI response:', payload.new)
  )
  .subscribe();
```

## 🛠️ Development Workflow - Windows

```cmd
REM Development with email testing
docker-compose -f docker-compose.yml -f docker/docker-compose.dev.yml up -d

REM Production-like setup
docker-compose up -d

REM View logs
docker-compose logs -f [service-name]

REM Reset and clean restart
scripts\reset.bat && docker-compose up -d
```

### **NPM Scripts (Windows-Optimized)** 🚀

For the best Windows developer experience, use the included npm scripts with Windows batch files:

```cmd
REM Quick start commands
npm run start                    # Start all services
npm run stop                     # Stop all services
npm run reset                    # Clean reset and restart
npm run logs                     # View all logs

REM First-time setup commands
npm run setup:first-time         # Complete automated setup with demo data
npm run seed                     # Seed database with demo users and projects
npm run setup:n8n                # Configure n8n workflows

REM Testing commands
npm test                         # Run all tests
npm run health                   # Health check
npm run test:auth                # Test authentication
npm run test:db                  # Test database integration

REM Utility commands
npm run dashboard:open           # Open dashboard in browser
npm run email:open               # Open email testing interface
npm run db:connect               # Connect to database
npm run email:open               # Open email interface (Windows)
npm run n8n:open                 # Open n8n workflows (Windows)
npm run kong:open                # Open Kong gateway (Windows)
```

## 🔧 Configuration & Customization

### **Environment Variables**

Key settings in `.env`:

```bash
# Database
POSTGRES_PASSWORD=your-super-secret-jwt-token-with-at-least-32-characters-long

# Authentication
ENABLE_EMAIL_SIGNUP=true
ENABLE_EMAIL_AUTOCONFIRM=false  # Set true for development
DISABLE_SIGNUP=false

# SMTP (for production)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# AI Service Keys (add as needed)
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
PERPLEXITY_API_KEY=pplx-...
```

### **AI Model Configuration**

Configure your AI integrations by setting environment variables in your `.env` file:

```bash
# AI Provider Keys
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
PERPLEXITY_API_KEY=pplx-...
GOOGLE_API_KEY=AIza...
```

## 🔐 Security & Production Features

### **Built-in Security**

- **JWT Authentication** - Secure token-based auth with proper expiration
- **Row Level Security** - Database-level authorization for multi-tenancy
- **API Gateway** - Kong providing rate limiting and access control
- **Environment Isolation** - Secure configuration management
- **Network Security** - Proper Docker networking and service isolation

### **Production Ready**

- **Connection Pooling** - PgBouncer for database performance
- **Health Monitoring** - Comprehensive service health checks
- **Logging Infrastructure** - Audit trails and error tracking
- **Data Persistence** - Proper volume management for production data
- **Backup Strategy** - Database persistence and recovery patterns

## 🎯 AI Use Case Examples

### **Chatbot with Memory**

1. **User authentication** via Kong gateway
2. **Conversation storage** in PostgreSQL
3. **Vector search** for context retrieval
4. **n8n workflow** for AI processing
5. **Real-time responses** via WebSocket

### **Document Analysis Pipeline**

1. **File upload** to Supabase Storage
2. **Background processing** via n8n workflows
3. **Vector embeddings** generated and stored
4. **Search API** for semantic queries
5. **Real-time results** via subscriptions

### **Content Generation System**

1. **Template management** in database
2. **Generation workflows** in n8n
3. **Approval processes** with user roles
4. **Version control** with audit trails
5. **API endpoints** for integration

## 📊 Performance & Scaling

### **Optimized for AI Workloads**

- **pgvector** configured for efficient similarity search
- **Connection pooling** via PgBouncer for high concurrency
- **Background processing** for expensive AI operations
- **Caching strategies** built into Kong gateway
- **Resource isolation** via Docker containers

### **Horizontal Scaling Ready**

- **Stateless services** for easy horizontal scaling
- **Database connection pooling** for multiple instances
- **Load balancing** via Kong gateway
- **Microservices architecture** with clear service boundaries

## 🚀 Deployment Options

### **Development**

- **Local Docker** - Full stack on your machine
- **Development email** - Inbucket for testing auth flows
- **Hot reloading** - Live development with instant feedback

### **Production**

- **Docker Compose** - Single-server deployment
- **Environment management** - Production-ready configuration
- **Health monitoring** - Built-in service monitoring
- **SSL/TLS ready** - HTTPS configuration templates

## 🔄 Migration & Upgrades

### **Easy Updates**

```cmd
REM Simple upgrade process
scripts\reset.bat               REM Clean state
docker-compose pull             REM Latest images
docker-compose up -d            REM Restart with updates
```

### **Data Migration**

- **PostgreSQL dumps** for data backup/restore
- **Volume persistence** maintains data across updates
- **Schema migrations** via SQL scripts
- **n8n workflows** exported/imported automatically

## 🤝 AI Integration

### **Built for AI Applications**

- **Multiple AI Providers** - OpenAI, Anthropic, Google, and more
- **Clear extension points** - Add AI capabilities without breaking existing code
- **Modular architecture** - Mix and match components as needed
- **Vector database ready** - pgvector for semantic search and RAG

### **Development Patterns**

- **Infrastructure-as-Code** - No manual setup or configuration
- **Testing automation** - Comprehensive validation for CI/CD
- **Documentation as code** - Self-documenting APIs and services
- **Extension templates** - Clear patterns for adding new AI features

## 📚 Documentation & Resources

### **Getting Started Guides**

- [**Setup Complete Guide**](./docs/SETUP_COMPLETE.md) - Complete setup guide and next steps
- [**Project Specifications**](./docs/PROJECT_SPECS.md) - Architecture and technical specifications
- [**MVP Requirements**](./docs/PULSE_AI_MVP_REQUIREMENTS.md) - AI-enhanced feature requirements
- [**Supabase n8n Integration**](./docs/SUPABASE_N8N_INTEGRATION.md) - Edge Functions and workflow integration
- [**Flowbite Integration Plan**](./docs/FLOWBITE_INTEGRATION_PLAN.md) - UI component integration strategy

### **Development Resources**

- [**API Testing**](./scripts/README.md) - Testing and validation scripts
- [**n8n Workflows**](./n8n/workflows/) - AI automation workflow templates
- [**Database Schema**](./volumes/db/) - PostgreSQL setup and extensions
- [**Docker Configurations**](./docker/) - Development and deployment compose files
- [**Frontend Components**](./frontend/components/) - React components and UI elements

## 🛟 Troubleshooting

### **Common Issues**

**Services not starting?**

```cmd
REM Check service status
docker-compose ps

REM View logs
docker-compose logs -f

REM Reset and restart
scripts\reset.bat && docker-compose up -d
```

**Authentication failing?**

```cmd
REM Test auth flow
node scripts/test-auth-complete.js

REM Check email service (dev mode)
start http://localhost:9000

REM Verify Kong routing
curl http://localhost:8000/auth/v1/health
```

**n8n workflows not working?**

```cmd
REM Restart n8n service
docker-compose restart n8n

REM Check n8n logs
docker-compose logs -f n8n

REM Access n8n interface
start http://localhost:5678
```

### **Reset Options**

```cmd
scripts\reset.bat                    REM Standard reset
scripts\reset.bat --help             REM See all options
```
