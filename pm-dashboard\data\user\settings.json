{"language": "English (US)", "timeZone": "GMT+0 Greenwich Mean Time (GMT)", "socialAccounts": [{"social": "Facebook", "account": "facebook.com/themesberg", "icon": "/images/logos/facebook.svg"}, {"social": "Twitter", "account": "twitter.com/themesberg", "icon": "/images/logos/twitter.svg"}, {"social": "<PERSON><PERSON><PERSON>", "account": "", "icon": "/images/logos/github.svg"}, {"social": "Dribble", "account": "", "icon": "/images/logos/dribble.svg"}], "otherAccounts": [{"name": "<PERSON>", "avatar": "/images/users/bonnie-green.png", "lastLocation": "New York, USA", "lastSeen": 1}, {"name": "<PERSON><PERSON>", "avatar": "/images/users/jese-leos.png", "lastLocation": "California, USA", "lastSeen": 2}, {"name": "<PERSON>", "avatar": "/images/users/thomas-lean.png", "lastLocation": "Texas, USA", "lastSeen": 60}, {"name": "<PERSON>", "avatar": "/images/users/lana-byrd.png", "lastLocation": "Texas, USA", "lastSeen": 60}], "sessions": [{"location": "California, USA", "ipAddress": "***************", "userAgent": "Chrome on macOS"}, {"location": "Rome, Italy", "ipAddress": "***************", "userAgent": "Safari on iPhone"}], "alertsNotifications": {"companyNews": true, "accountActivity": true, "meetups": true, "newMessages": false}, "emailNotifications": {"ratingReminders": false, "itemUpdates": true, "itemComments": true, "buyerReviews": true}}