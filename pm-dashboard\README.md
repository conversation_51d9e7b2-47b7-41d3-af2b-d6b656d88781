# Pulse - AI-Powered Project Management Dashboard

A modern, AI-enhanced project management dashboard built with Next.js, Supabase, and Docker. Transform your team's productivity with intelligent project tracking, automated task management, and real-time collaboration.

**✨ Windows-Optimized** - All scripts and development workflows are designed for Windows environments.

## ✨ Features

- **🏢 Multi-workspace Support** - Organize projects across different teams and organizations
- **📋 Project & Task Management** - Full CRUD operations with subtasks and dependencies
- **👥 Team Collaboration** - Real-time comments, mentions, and activity feeds
- **📊 Kanban Boards** - Visual task management with drag-and-drop functionality
- **📈 Analytics Dashboard** - Track project progress and team productivity
- **🔐 Secure Authentication** - Built-in user management with role-based permissions
- **📁 File Attachments** - Upload and manage project files
- **⏱️ Time Tracking** - Log time spent on tasks and generate reports
- **🔔 Real-time Notifications** - Stay updated with instant notifications
- **🌙 Dark Mode Support** - Beautiful UI that works in any lighting

## 🚀 Quick Start

### Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- Git

### Setup

1. **Clone the repository**
   ```cmd
   git clone <your-repo-url>
   cd pulse-project-management
   ```

2. **Start all services**
   ```cmd
   npm run start
   ```

3. **Access your application**
   - **Application**: http://localhost:3000
   - **Supabase API**: http://localhost:8000
   - **Email Testing**: http://localhost:9000 (Inbucket)
   - **Database**: localhost:5432

## 🛠️ Development

### Available Scripts (Windows)

```cmd
REM Development
npm run start               REM Start all services
npm run stop                REM Stop all services
npm run restart             REM Restart all services
npm run reset               REM Reset database and restart services
npm run logs                REM View logs from all services

REM Testing
npm run health              REM Check service health
npm run test                REM Run all tests
npm run test:auth           REM Test authentication
npm run test:db             REM Test database integration

REM Utilities
npm run dashboard:open      REM Open dashboard in browser
npm run email:open          REM Open email testing interface
npm run db:connect          REM Connect to database
```

### Development Workflow (Windows)

1. **Start development environment**
   ```cmd
   npm run start
   ```

2. **Make your changes**
   - The app will hot-reload automatically
   - Database changes persist in Docker volumes

3. **View logs**
   ```cmd
   npm run logs
   ```

4. **Reset database if needed**
   ```cmd
   npm run reset
   ```

## 🏗️ Architecture

### Tech Stack

- **Frontend**: Next.js 15 + React 19 + TypeScript
- **UI Framework**: Flowbite React + Tailwind CSS
- **Backend**: Self-hosted Supabase
  - PostgreSQL database
  - Authentication (GoTrue)
  - Real-time subscriptions
  - File storage
  - REST API (PostgREST)
- **Containerization**: Docker + Docker Compose

### Services

| Service | Port | Description |
|---------|------|-------------|
| Next.js App | 3000 | Main application |
| Supabase API | 8000 | API Gateway (Kong) |
| PostgreSQL | 5432 | Database |
| Inbucket | 9000 | Email testing |

## 🔧 Configuration

### Environment Variables

Copy `.env.local.example` to `.env.local` and customize:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=http://localhost:8000
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🔐 Security

- **Row Level Security (RLS)** enabled on all tables
- **JWT-based authentication** with secure token handling
- **Role-based permissions** for workspace and project access
- **Input validation** and sanitization
- **CORS configuration** for API security

## 🆘 Troubleshooting

### Common Issues

**Services won't start**
```cmd
REM Check Docker status
docker-compose ps

REM View logs
docker-compose logs

REM Reset everything
npm run reset
```

**Database connection issues**
```cmd
REM Check database logs
docker-compose logs db

REM Restart database
docker-compose restart db
```

### Getting Help

- Check the logs: `npm run logs`
- Reset the environment: `npm run reset`
- Test authentication: `npm run test:auth`

---

**Built with ❤️ using Next.js, Supabase, and Docker**
