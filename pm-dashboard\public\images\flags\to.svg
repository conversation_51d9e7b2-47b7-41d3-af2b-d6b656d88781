<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.25" y="0.25" width="27.5" height="19.5" rx="1.75" fill="white" stroke="#F5F5F5" stroke-width="0.5"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect x="0.25" y="0.25" width="27.5" height="19.5" rx="1.75" fill="white" stroke="white" stroke-width="0.5"/>
</mask>
<g mask="url(#mask0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28 0H16V10.6667H0V20H28V0Z" fill="#E20F1B"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.33325C6.81591 1.33325 6.66667 1.48249 6.66667 1.66659V3.99992H4.33333C4.14924 3.99992 4 4.14916 4 4.33325V6.33325C4 6.51735 4.14924 6.66659 4.33333 6.66659H6.66667V8.99992C6.66667 9.18401 6.81591 9.33325 7 9.33325H9C9.18409 9.33325 9.33333 9.18401 9.33333 8.99992V6.66659H11.6667C11.8508 6.66659 12 6.51735 12 6.33325V4.33325C12 4.14916 11.8508 3.99992 11.6667 3.99992H9.33333V1.66659C9.33333 1.48249 9.18409 1.33325 9 1.33325H7Z" fill="url(#paint0_linear)"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="4" y1="1.33325" x2="4" y2="9.33325" gradientUnits="userSpaceOnUse">
<stop stop-color="#DF101B"/>
<stop offset="1" stop-color="#C00711"/>
</linearGradient>
</defs>
</svg>
