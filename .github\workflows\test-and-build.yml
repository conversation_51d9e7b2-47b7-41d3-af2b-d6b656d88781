name: Test and Build

on:
  pull_request:
    branches: [main, develop]
  push:
    branches: [develop]

jobs:
  test-dashboard:
    name: Test Dashboard
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./pm-dashboard
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Run type check
        run: bun run typecheck

      - name: Run linting
        run: bun run lint

      - name: Check formatting
        run: bun run format:check

      - name: Run tests
        run: bun test

      - name: Build application
        run: bun run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: http://localhost:8000
          NEXT_PUBLIC_SUPABASE_ANON_KEY: dummy-key-for-build

  test-docker:
    name: Test Docker Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Test development build
        uses: docker/build-push-action@v5
        with:
          context: ./pm-dashboard
          file: ./pm-dashboard/Dockerfile.dev
          push: false
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Test production build
        uses: docker/build-push-action@v5
        with:
          context: ./pm-dashboard
          file: ./pm-dashboard/Dockerfile
          push: false
          cache-from: type=gha
          cache-to: type=gha,mode=max

  test-compose:
    name: Test Docker Compose
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create test environment file
        run: |
          cp .env.example .env
          # Set test values
          sed -i 's/your_postgres_password_here/test_password_123/g' .env
          sed -i 's/your_jwt_secret_here/test_jwt_secret_at_least_32_characters_long/g' .env

      - name: Test initialization
        run: |
          docker compose --profile init config
          echo "✅ Initialization profile validated"

      - name: Test development profile
        run: |
          docker compose --profile dev config
          echo "✅ Development profile validated"

      - name: Test production profile
        run: |
          docker compose --profile prod config
          echo "✅ Production profile validated"

      - name: Test service startup (basic)
        run: |
          # Start core services for basic testing
          docker compose up -d db
          sleep 10
          
          # Test database connectivity
          docker compose exec -T db pg_isready -U postgres
          echo "✅ Database connectivity test passed"
          
          # Cleanup
          docker compose down -v

  security-check:
    name: Security Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run security audit
        working-directory: ./pm-dashboard
        run: |
          bun audit || echo "Security audit completed with warnings"

      - name: Check for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD

  validate-config:
    name: Validate Configuration
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate Docker Compose
        run: |
          docker compose config --quiet
          echo "✅ Docker Compose configuration is valid"

      - name: Validate environment template
        run: |
          if [ ! -f .env.example ]; then
            echo "❌ .env.example file is missing"
            exit 1
          fi
          echo "✅ Environment template exists"

      - name: Check required scripts
        run: |
          required_scripts=(
            "scripts/init/setup-environment.sh"
            "scripts/seed/smart-seed.sh"
            "scripts/reset/selective-reset.sh"
            "config/n8n/import-workflows.sh"
          )
          
          for script in "${required_scripts[@]}"; do
            if [ ! -f "$script" ]; then
              echo "❌ Required script missing: $script"
              exit 1
            fi
          done
          echo "✅ All required scripts present"
