"use client";

import { useAuth } from "@/contexts/AuthContext";
import type { PropsWithChildren } from "react";

interface AuthWrapperProps extends PropsWithChildren {
  fallback?: React.ReactNode;
  requireAuth?: boolean;
  requireRole?: string;
}

export function AuthWrapper({ 
  children, 
  fallback = null, 
  requireAuth = true,
  requireRole 
}: AuthWrapperProps) {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  // Check authentication requirement
  if (requireAuth && !user) {
    return <>{fallback}</>;
  }

  // Check role requirement
  if (requireRole && user) {
    const userRole = user.app_metadata?.role || user.user_metadata?.role || "user";
    if (userRole !== requireRole && requireRole !== "user") {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
}