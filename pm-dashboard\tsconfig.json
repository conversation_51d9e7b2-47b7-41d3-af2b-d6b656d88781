{"compilerOptions": {"allowJs": false, "esModuleInterop": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "moduleResolution": "bundler", "noEmit": true, "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}], "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "es5"}, "exclude": ["node_modules"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"]}