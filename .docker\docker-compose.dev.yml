# Development overrides for Pulse Project Management
# Usage: docker-compose -f docker-compose.yml -f .docker/docker-compose.dev.yml up -d

name: pulse-project-management

services:
  # Override dashboard for development
  dashboard:
    build:
      context: ./pm-dashboard
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_SUPABASE_URL: http://localhost:8000
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SERVICE_ROLE_KEY}
      WATCHPACK_POLLING: true
      # Enable hot reload for Docker
      CHOKIDAR_USEPOLLING: true
      # Next.js dev server configuration
      NEXT_DEV_HOST: 0.0.0.0
    volumes:
      - ./pm-dashboard:/app
      - /app/node_modules
      - /app/.next
    command: ["bun", "run", "dev", "--", "--hostname", "0.0.0.0"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Development email server for testing auth flows
  inbucket:
    container_name: pulse-inbucket
    image: inbucket/inbucket:stable
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "9000:9000"  # Web interface
      - "2500:2500"  # SMTP
      - "1100:1100"  # POP3
    environment:
      INBUCKET_WEB_ADDR: 0.0.0.0:9000
      INBUCKET_SMTP_ADDR: 0.0.0.0:2500
      INBUCKET_POP3_ADDR: 0.0.0.0:1100
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Override auth service to use development email
  auth:
    environment:
      GOTRUE_SMTP_HOST: inbucket
      GOTRUE_SMTP_PORT: 2500
      GOTRUE_SMTP_USER: ""
      GOTRUE_SMTP_PASS: ""
      GOTRUE_MAILER_AUTOCONFIRM: ${ENABLE_EMAIL_AUTOCONFIRM:-false}
    depends_on:
      - inbucket
      - db
      - analytics

networks:
  pulse-network:
    driver: bridge
