"use client";

import {
  Checkbox,
  Label,
  Progress,
  Table,
  TableBody,
  TableCell,
  TableRow,
} from "flowbite-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import {
  HiArchive,
  HiChevronLeft,
  HiChevronRight,
  HiDotsVertical,
  HiExclamationCircle,
  HiEye,
  HiOutlineStar,
  HiOutlineViewGrid,
  HiPlusSm,
  HiStar,
  HiTrash,
  HiViewGrid,
} from "react-icons/hi";
import { twMerge } from "tailwind-merge";
import type { MailingInboxPageData } from "./page";

function MailingInboxPageContent({ inboxMessages }: MailingInboxPageData) {
  return (
    <>
      <Menu inboxMessages={inboxMessages} />
      <Inbox inboxMessages={inboxMessages} />
      <Footer />
    </>
  );
}

function Menu({ inboxMessages }: MailingInboxPageData) {
  const [page, setPage] = useState(0);
  const numEntriesPerPage = Math.min(20, inboxMessages.length);
  const numPages = Math.floor(inboxMessages.length / numEntriesPerPage);

  const previousPage = () => {
    setPage(page > 0 ? page - 1 : page);
  };

  const nextPage = () => {
    setPage(page < numPages - 1 ? page + 1 : page);
  };

  return (
    <div className="block items-center justify-between border-b border-gray-200 bg-white p-4 sm:flex dark:border-gray-700 dark:bg-gray-800">
      <div className="flex items-center divide-x divide-gray-100 dark:divide-gray-700">
        <div className="pr-3">
          <Label htmlFor="checkbox-all" className="sr-only">
            Select all
          </Label>
          <Checkbox
            id="checkbox-all"
            name="checkbox-all"
            className="align-middle"
          />
        </div>
        <div className="flex space-x-2 px-0 sm:px-2">
          <Link
            href="#"
            className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
          >
            <span className="sr-only">Delete</span>
            <HiTrash className="text-2xl" />
          </Link>
          <Link
            href="#"
            className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
          >
            <span className="sr-only">Move</span>
            <HiArchive className="text-2xl" />
          </Link>
          <Link
            href="#"
            className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
          >
            <span className="sr-only">Purge</span>
            <HiExclamationCircle className="text-2xl" />
          </Link>
          <Link
            href="#"
            className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
          >
            <span className="sr-only">Settings</span>
            <HiDotsVertical className="text-2xl" />
          </Link>
        </div>
        <div className="pl-3">
          <Link
            href="/mailing/compose"
            className="bg-primary-700 hover:bg-primary-800 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800 mr-3 inline-flex items-center gap-x-2 rounded-lg px-5 py-2.5 text-center text-sm font-medium text-white focus:ring-4"
          >
            <HiPlusSm className="h-5 w-5" />
            Compose
          </Link>
        </div>
      </div>
      <div className="hidden items-center space-y-3 space-x-0 sm:flex sm:space-y-0 sm:space-x-3">
        <Link
          href="#"
          className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
        >
          <span className="sr-only">Apps</span>
          <HiOutlineViewGrid className="h-7 w-7" />
        </Link>
        <button
          onClick={previousPage}
          className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
        >
          <span className="sr-only">Previous</span>
          <HiChevronLeft className="h-7 w-7" />
        </button>
        <button
          onClick={nextPage}
          className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
        >
          <span className="sr-only">Next</span>
          <HiChevronRight className="h-7 w-7" />
        </button>
        <span className="font-normal text-gray-500 sm:text-xs md:text-sm dark:text-gray-400">
          Show&nbsp;
          <span className="font-semibold text-gray-900 dark:text-white">
            {page * inboxMessages.length + 1}-
            {numEntriesPerPage * page + numEntriesPerPage}
          </span>
          &nbsp;of&nbsp;
          <span className="font-semibold text-gray-900 dark:text-white">
            {inboxMessages.length}
          </span>
        </span>
      </div>
    </div>
  );
}

function Inbox({ inboxMessages }: MailingInboxPageData) {
  const router = useRouter();

  function onRowClick() {
    router.push("/mailing/read");
  }

  function onRowSelect(e: React.MouseEvent) {
    e.stopPropagation();
  }

  function onRowStar(e: React.MouseEvent) {
    e.stopPropagation();
  }

  return (
    <div className="flex flex-col">
      <div className="overflow-x-auto">
        <div className="inline-block min-w-full align-middle">
          <div className="overflow-hidden shadow">
            <Table className="min-w-full divide-y divide-gray-200">
              <TableBody
                theme={{ cell: { base: "rounded-none" } }}
                className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800"
              >
                {inboxMessages.map(
                  ({ avatar, sender, subject, time, read, starred }) => (
                    <TableRow
                      key={`${sender}-${time}-${subject}`}
                      className="cursor-pointer bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-600"
                      onClick={onRowClick}
                    >
                      <TableCell className="w-4 p-4">
                        <div className="inline-flex items-center space-x-4">
                          <Checkbox id="checkbox-1" onClick={onRowSelect} />
                          <button onClick={onRowStar}>
                            {starred ? (
                              <HiStar className="h-6 w-6 text-gray-500 hover:text-yellow-300 dark:text-gray-400 dark:hover:text-yellow-300" />
                            ) : (
                              <HiOutlineStar className="h-6 w-6 text-gray-500 hover:text-yellow-300 dark:text-gray-400 dark:hover:text-yellow-300" />
                            )}
                          </button>
                        </div>
                      </TableCell>
                      <TableCell className="relative flex items-center space-x-4 p-4 whitespace-nowrap">
                        <Image
                          alt=""
                          height={24}
                          src={avatar}
                          width={24}
                          className="rounded-full"
                        />
                        <span
                          className={twMerge(
                            "text-base text-gray-700 after:absolute after:inset-0 dark:text-gray-400",
                            !read &&
                              "font-semibold text-gray-900 dark:text-white",
                          )}
                        >
                          {sender}
                        </span>
                      </TableCell>
                      <TableCell
                        className={twMerge(
                          "max-w-sm truncate overflow-hidden p-4 text-base text-gray-700 xl:max-w-(--breakpoint-md) 2xl:max-w-(--breakpoint-lg) dark:text-gray-400",
                          !read &&
                            "font-semibold text-gray-900 dark:text-white",
                        )}
                      >
                        {subject}
                      </TableCell>
                      <TableCell
                        className={twMerge(
                          "p-4 text-base whitespace-nowrap text-gray-700 dark:text-gray-400",
                          !read && "font-medium text-gray-900 dark:text-white",
                        )}
                      >
                        {time}
                      </TableCell>
                    </TableRow>
                  ),
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}

function Footer() {
  return (
    <div className="w-full items-center space-y-4 border-t border-gray-200 bg-white p-4 sm:sticky sm:flex sm:justify-between sm:space-y-0 dark:border-gray-700 dark:bg-gray-800">
      <div className="flex flex-col gap-1">
        <div className="text-xs font-medium text-gray-500 dark:text-gray-400">
          3.24 GB of 15 GB used
        </div>
        <Progress
          color="blue"
          progress={22}
          className="w-full sm:w-52 md:w-96"
        />
      </div>
      <div className="hidden items-center gap-x-2 text-sm font-medium text-gray-500 sm:flex dark:text-gray-400">
        Last account activity: 2 hours ago
        <HiEye className="h-4 w-4" />
      </div>
      <div className="mb-4 flex items-center sm:mb-0 sm:hidden">
        <Link
          href="#"
          className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900"
        >
          <span className="sr-only">Apps</span>
          <HiViewGrid className="text-2xl" />
        </Link>
        <Link
          href="#"
          className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900"
        >
          <span className="sr-only">Previous</span>
          <HiChevronLeft className="text-2xl" />
        </Link>
        <Link
          href="#"
          className="inline-flex cursor-pointer justify-center rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-900"
        >
          <span className="sr-only">Next</span>
          <HiChevronRight className="text-2xl" />
        </Link>
        <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
          Showing&nbsp;
          <span className="font-semibold text-gray-900 dark:text-white">
            1-25
          </span>
          &nbsp;of&nbsp;
          <span className="font-semibold text-gray-900 dark:text-white">
            2290
          </span>
        </span>
      </div>
    </div>
  );
}

export default MailingInboxPageContent;
