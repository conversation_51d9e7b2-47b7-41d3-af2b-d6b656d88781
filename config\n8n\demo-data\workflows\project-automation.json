{"name": "Project Task Automation", "nodes": [{"parameters": {}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "project-task-webhook"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.action}}", "operation": "equal", "value2": "task_created"}]}}, "id": "task-created-filter", "name": "Task Created Filter", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "http://dashboard:3000/api/ai/prioritize", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}", "Content-Type": "application/json"}}, "jsonParameters": true, "bodyParametersJson": "={\n  \"task_id\": \"{{$json.task.id}}\",\n  \"title\": \"{{$json.task.title}}\",\n  \"description\": \"{{$json.task.description}}\",\n  \"project_context\": \"{{$json.project.name}}\"\n}"}, "id": "ai-prioritize", "name": "AI Task Prioritization", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 200]}, {"parameters": {"url": "http://dashboard:3000/api/notifications/send", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}", "Content-Type": "application/json"}}, "jsonParameters": true, "bodyParametersJson": "={\n  \"type\": \"task_assignment\",\n  \"recipient\": \"{{$json.task.assigned_to}}\",\n  \"data\": {\n    \"task_title\": \"{{$json.task.title}}\",\n    \"project_name\": \"{{$json.project.name}}\",\n    \"due_date\": \"{{$json.task.due_date}}\"\n  }\n}"}, "id": "send-notification", "name": "Send Assignment Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Task Created Filter", "type": "main", "index": 0}]]}, "Task Created Filter": {"main": [[{"node": "AI Task Prioritization", "type": "main", "index": 0}, {"node": "Send Assignment Notification", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "UTC"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "project-task-automation", "tags": []}