"use client";

import { <PERSON><PERSON>, Card, Checkbox, Label, TextInput } from "flowbite-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

export default function SignInPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  
  const { signIn } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!email || !password) {
      setError("Email and password are required");
      return;
    }

    setLoading(true);

    try {
      const { data, error } = await signIn(email, password);
      
      if (error) {
        setError(error.message);
      } else {
        // Redirect to dashboard on successful login
        router.push("/");
      }
    } catch (err) {
      setError("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="mx-auto flex flex-col items-center justify-center px-6 pt-8 md:h-screen">
      <Link
        href="/"
        className="mb-8 flex items-center justify-center text-2xl font-semibold lg:mb-10 dark:text-white"
      >
        <span className="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">
          Pulse
        </span>
      </Link>
      <Card
        horizontal
        imgAlt=""
        imgSrc="/images/authentication/login.jpg"
        className="w-full md:max-w-(--breakpoint-lg)"
        theme={{
          root: {
            children: "my-auto w-full gap-0 space-y-8 p-6 sm:p-8 lg:p-16",
          },
          img: {
            horizontal: {
              on: "hidden w-2/3 rounded-l-lg md:w-96 md:p-0 lg:block",
            },
          },
        }}
      >
        <h2 className="text-2xl font-bold text-gray-900 lg:text-3xl dark:text-white">
          Sign in to Pulse
        </h2>
        
        {error && (
          <div className="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400">
            {error}
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="email">Your email</Label>
            <TextInput
              id="email"
              name="email"
              placeholder="<EMAIL>"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="password">Your password</Label>
            <TextInput
              id="password"
              name="password"
              placeholder="••••••••"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-x-3">
              <Checkbox 
                id="rememberMe" 
                name="rememberMe"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              <Label htmlFor="rememberMe">Remember me</Label>
            </div>
            <Link
              href="/authentication/forgot-password"
              className="text-primary-700 dark:text-primary-500 text-right text-sm hover:underline"
            >
              Lost Password?
            </Link>
          </div>
          <div className="mb-6">
            <Button
              size="lg"
              color="blue"
              type="submit"
              className="w-full sm:w-auto"
              disabled={loading}
            >
              {loading ? "Signing in..." : "Login to your account"}
            </Button>
          </div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Not registered?&nbsp;
            <Link
              href="/authentication/sign-up"
              className="text-primary-700 dark:text-primary-500 hover:underline"
            >
              Create account
            </Link>
          </p>
        </form>
      </Card>
    </div>
  );
}
