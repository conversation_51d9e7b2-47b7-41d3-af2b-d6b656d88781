#!/usr/bin/env node

/**
 * Postinstall script for Pulse Dashboard
 * Handles Flowbite React patch in Docker and local environments
 */

const { execSync } = require('child_process');
const { existsSync, mkdirSync, writeFileSync } = require('fs');
const path = require('path');

console.log('🔧 Running postinstall setup...');

// Skip postinstall if explicitly requested (during Docker build)
if (process.env.SKIP_POSTINSTALL === '1') {
  console.log('⚠️  Skipping postinstall (SKIP_POSTINSTALL=1)');
  process.exit(0);
}

try {
  // Get the project root directory (works in both Docker and local environments)
  const projectRoot = process.cwd();
  
  // Ensure .flowbite-react directory exists
  const flowbiteDir = path.join(projectRoot, '.flowbite-react');
  if (!existsSync(flowbiteDir)) {
    console.log('📁 Creating .flowbite-react directory...');
    mkdirSync(flowbiteDir, { recursive: true, mode: 0o755 });
  }

  // Ensure class-list.json exists
  const classListFile = path.join(flowbiteDir, 'class-list.json');
  if (!existsSync(classListFile)) {
    console.log('📄 Creating class-list.json...');
    writeFileSync(classListFile, '{}', 'utf8');
  }

  // Run flowbite-react patch
  console.log('🎨 Running Flowbite React patch...');
  execSync('flowbite-react patch', { 
    stdio: 'inherit',
    cwd: projectRoot
  });
  
  console.log('✅ Postinstall completed successfully!');
} catch (error) {
  console.warn('⚠️  Postinstall warning:', error.message);
  console.log('📋 Continuing without Flowbite patch...');
  // Don't fail the build, just warn
  process.exit(0);
}