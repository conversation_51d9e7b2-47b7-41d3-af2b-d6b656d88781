"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useMemo } from "react";

export function useUserPermissions() {
  const { user } = useAuth();

  const permissions = useMemo(() => {
    if (!user) {
      return {
        canCreateProjects: false,
        canDeleteProjects: false,
        canManageUsers: false,
        canViewAnalytics: false,
        isAdmin: false,
        isPremium: false,
      };
    }

    // Extract role from user metadata or app_metadata
    const role = user.app_metadata?.role || user.user_metadata?.role || "user";
    const subscription = user.app_metadata?.subscription || "free";

    return {
      canCreateProjects: true, // All authenticated users can create projects
      canDeleteProjects: role === "admin" || role === "owner",
      canManageUsers: role === "admin" || role === "owner",
      canViewAnalytics: subscription === "premium" || role === "admin",
      isAdmin: role === "admin" || role === "owner",
      isPremium: subscription === "premium",
      role,
      subscription,
    };
  }, [user]);

  return {
    user,
    ...permissions,
  };
}