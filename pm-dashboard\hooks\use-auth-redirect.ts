"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

/**
 * Hook to redirect users based on authentication status
 * @param redirectTo - Where to redirect authenticated users (default: "/")
 * @param redirectFrom - Where to redirect unauthenticated users (default: "/authentication/sign-in")
 */
export function useAuthRedirect(
  redirectTo: string = "/",
  redirectFrom: string = "/authentication/sign-in"
) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (loading) return;

    // If user is authenticated and on auth pages, redirect to dashboard
    if (user && window.location.pathname.includes("/authentication/")) {
      router.push(redirectTo);
    }

    // If user is not authenticated and on protected pages, redirect to sign-in
    if (!user && !window.location.pathname.includes("/authentication/")) {
      router.push(redirectFrom);
    }
  }, [user, loading, router, redirectTo, redirectFrom]);

  return { user, loading };
}