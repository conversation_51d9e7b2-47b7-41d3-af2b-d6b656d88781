{"createdAt": "2025-01-17T21:45:00.000Z", "updatedAt": "2025-01-17T21:45:00.000Z", "id": "pulseTaskAuto001", "name": "Pulse Task Automation", "active": true, "nodes": [{"parameters": {"httpMethod": "POST", "path": "task-created", "options": {}}, "id": "webhook-task-created", "name": "Task Created Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "pulse-task-created-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "priority-check", "leftValue": "={{ $json.priority }}", "rightValue": "high", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-priority", "name": "Check if High Priority", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"authentication": "serviceAccount", "resource": "database", "operation": "select", "table": "profiles", "where": {"conditions": [{"column": "id", "condition": "equals", "value": "={{ $json.assigned_to }}"}]}}, "id": "get-assignee-profile", "name": "Get Assignee Profile", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email }}", "subject": "🚨 High Priority Task Assigned: {{ $('Task Created Webhook').item.json.title }}", "text": "Hi {{ $json.username }},\n\nYou have been assigned a high priority task:\n\n**{{ $('Task Created Webhook').item.json.title }}**\n\nDescription: {{ $('Task Created Webhook').item.json.description }}\n\nDue Date: {{ $('Task Created Webhook').item.json.due_date }}\n\nPlease prioritize this task and update its status as you make progress.\n\nBest regards,\nPulse Project Management", "options": {}}, "id": "send-notification-email", "name": "Send High Priority Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"authentication": "serviceAccount", "resource": "database", "operation": "insert", "table": "task_notifications", "columns": {"mappingMode": "defineBelow", "value": {"task_id": "={{ $('Task Created Webhook').item.json.id }}", "user_id": "={{ $('Task Created Webhook').item.json.assigned_to }}", "type": "high_priority_assignment", "message": "You have been assigned a high priority task: {{ $('Task Created Webhook').item.json.title }}", "sent_at": "={{ new Date().toISOString() }}"}}}, "id": "log-notification", "name": "Log Notification", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"httpMethod": "POST", "path": "task-completed", "options": {}}, "id": "webhook-task-completed", "name": "Task Completed Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 500], "webhookId": "pulse-task-completed-webhook"}, {"parameters": {"authentication": "serviceAccount", "resource": "database", "operation": "select", "table": "projects", "where": {"conditions": [{"column": "id", "condition": "equals", "value": "={{ $json.project_id }}"}]}}, "id": "get-project-info", "name": "Get Project Info", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [460, 500]}, {"parameters": {"authentication": "serviceAccount", "resource": "database", "operation": "select", "table": "tasks", "where": {"conditions": [{"column": "project_id", "condition": "equals", "value": "={{ $json.project_id }}"}, {"column": "status", "condition": "notEquals", "value": "done"}]}}, "id": "check-remaining-tasks", "name": "Check Remaining Tasks", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [680, 500]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "tasks-remaining", "leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-project-completion", "name": "Check if Project Complete", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 500]}, {"parameters": {"authentication": "serviceAccount", "resource": "database", "operation": "update", "table": "projects", "where": {"conditions": [{"column": "id", "condition": "equals", "value": "={{ $('Task Completed Webhook').item.json.project_id }}"}]}, "columns": {"mappingMode": "defineBelow", "value": {"status": "completed", "completed_at": "={{ new Date().toISOString() }}"}}}, "id": "mark-project-complete", "name": "Mark Project Complete", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1120, 400]}], "connections": {"Task Created Webhook": {"main": [[{"node": "Check if High Priority", "type": "main", "index": 0}]]}, "Check if High Priority": {"main": [[{"node": "Get Assignee Profile", "type": "main", "index": 0}]]}, "Get Assignee Profile": {"main": [[{"node": "Send High Priority Notification", "type": "main", "index": 0}]]}, "Send High Priority Notification": {"main": [[{"node": "Log Notification", "type": "main", "index": 0}]]}, "Task Completed Webhook": {"main": [[{"node": "Get Project Info", "type": "main", "index": 0}]]}, "Get Project Info": {"main": [[{"node": "Check Remaining Tasks", "type": "main", "index": 0}]]}, "Check Remaining Tasks": {"main": [[{"node": "Check if Project Complete", "type": "main", "index": 0}]]}, "Check if Project Complete": {"main": [[{"node": "Mark Project Complete", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 2, "versionId": "1"}