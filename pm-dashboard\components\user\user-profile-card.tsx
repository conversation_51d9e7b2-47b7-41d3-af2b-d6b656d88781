"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useUserPermissions } from "@/hooks/use-user-permissions";
import { AuthWrapper } from "@/components/auth/auth-wrapper";
import { Avatar, <PERSON>ge, <PERSON><PERSON>, Card } from "flowbite-react";
import { HiCog, HiMail, HiCalendar, HiShieldCheck } from "react-icons/hi";

export function UserProfileCard() {
  const { user, loading } = useAuth();
  const { isAdmin, isPremium, role, subscription } = useUserPermissions();

  if (loading) {
    return (
      <Card>
        <div className="animate-pulse space-y-4">
          <div className="flex items-center space-x-4">
            <div className="rounded-full bg-gray-200 h-12 w-12"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-32"></div>
              <div className="h-3 bg-gray-200 rounded w-24"></div>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <AuthWrapper fallback={<div>Please sign in to view profile</div>}>
      <Card>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <Avatar
              img="/images/users/default-avatar.png"
              alt={user?.email || "User"}
              size="lg"
              rounded
            />
            <div>
              <div className="flex items-center gap-2 mb-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {user?.user_metadata?.full_name || user?.email?.split('@')[0] || "User"}
                </h3>
                {isAdmin && (
                  <Badge color="red" size="sm">
                    <HiShieldCheck className="mr-1 h-3 w-3" />
                    Admin
                  </Badge>
                )}
                {isPremium && (
                  <Badge color="yellow" size="sm">
                    Premium
                  </Badge>
                )}
              </div>
              
              <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center gap-2">
                  <HiMail className="h-4 w-4" />
                  <span>{user?.email}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <HiCalendar className="h-4 w-4" />
                  <span>
                    Joined {user?.created_at ? new Date(user.created_at).toLocaleDateString() : "Unknown"}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-xs">
                    Role: <span className="font-medium capitalize">{role}</span> • 
                    Plan: <span className="font-medium capitalize">{subscription}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <Button size="sm" color="gray" outline>
            <HiCog className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </Card>
    </AuthWrapper>
  );
}