#!/bin/sh

# Docker entrypoint script for Next.js dashboard
# Handles Flowbite React permission setup and development server startup

echo "🚀 Starting Pulse Dashboard..."
echo "📦 Setting up directory permissions..."

# Ensure .next directory exists with proper permissions
if [ ! -d "/app/.next" ]; then
    echo "📁 Creating .next directory..."
    mkdir -p /app/.next
    chmod 755 /app/.next 2>/dev/null || echo "⚠️  Could not set .next permissions"
fi

# Ensure .flowbite-react directory exists with proper permissions
if [ ! -d "/app/.flowbite-react" ]; then
    echo "📁 Creating .flowbite-react directory..."
    mkdir -p /app/.flowbite-react
    chmod 755 /app/.flowbite-react 2>/dev/null || echo "⚠️  Could not set .flowbite-react permissions"
fi

# Check if we can write to the directory
if [ -w "/app/.flowbite-react" ]; then
    echo "✅ .flowbite-react directory is writable"
    
    # Create class-list.json if it doesn't exist
    if [ ! -f "/app/.flowbite-react/class-list.json" ]; then
        echo "📄 Creating class-list.json..."
        echo "{}" > /app/.flowbite-react/class-list.json 2>/dev/null || echo "⚠️  Could not create class-list.json, continuing anyway..."
        chmod 644 /app/.flowbite-react/class-list.json 2>/dev/null || echo "⚠️  Could not set class-list.json permissions"
    fi
    
    # Run flowbite-react patch if available
    echo "🔧 Running Flowbite React patch..."
    bun run postinstall 2>/dev/null || echo "⚠️  Flowbite patch failed, continuing anyway..."
else
    echo "⚠️  .flowbite-react directory not writable, continuing without patch..."
fi

# Start the development server
echo "🌐 Starting Next.js development server..."
if ! bun run dev --hostname 0.0.0.0 --port 3000; then
    echo "❌ Failed to start development server"
    exit 1
fi