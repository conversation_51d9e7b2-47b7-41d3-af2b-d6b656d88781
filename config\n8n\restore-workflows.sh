#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 n8n Workflow Restore${NC}"
echo "========================"

# Configuration
N8N_URL="http://n8n:5678"
BACKUP_DIR="/seed/backups"
RESTORE_PATH="$1"

# Check if restore path is provided
if [ -z "$RESTORE_PATH" ]; then
    echo "📁 Available backups:"
    ls -la "$BACKUP_DIR" | grep "n8n_backup_" | awk '{print "   " $9}' || echo "   No backups found"
    echo ""
    echo "Usage: $0 <backup_path>"
    echo "Example: $0 /seed/backups/n8n_backup_20240119_143022"
    exit 1
fi

# Check if backup exists
if [ ! -d "$RESTORE_PATH" ]; then
    echo -e "${RED}❌ Backup path does not exist: $RESTORE_PATH${NC}"
    exit 1
fi

echo "📁 Restore source: $RESTORE_PATH"

# Check if n8n is accessible
echo "🔍 Checking n8n accessibility..."
if ! curl -s -f "$N8N_URL/healthz" >/dev/null 2>&1; then
    echo -e "${RED}❌ n8n is not accessible${NC}"
    exit 1
fi

echo -e "${GREEN}✅ n8n is accessible${NC}"

# Read backup metadata
if [ -f "$RESTORE_PATH/backup_info.json" ]; then
    echo "📋 Backup information:"
    cat "$RESTORE_PATH/backup_info.json" | jq -r '
        "   Date: " + .date + "\n" +
        "   Type: " + .backup_type + "\n" +
        "   n8n Version: " + .n8n_version
    ' 2>/dev/null || echo "   Metadata available but not readable"
    echo ""
fi

# Confirm restore
echo -e "${YELLOW}⚠️ This will replace all existing workflows and credentials${NC}"
read -p "Continue with restore? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Restore cancelled"
    exit 0
fi

# Stop all active workflows
echo "⏹️ Stopping active workflows..."
n8n update:workflow --all --active=false || echo "   Some workflows may not have been stopped"

# Import credentials
if [ -d "$RESTORE_PATH/credentials" ]; then
    echo "🔑 Restoring credentials..."
    if n8n import:credentials --separate --input="$RESTORE_PATH/credentials"; then
        echo -e "${GREEN}✅ Credentials restored successfully${NC}"
    else
        echo -e "${YELLOW}⚠️ Credential restore failed${NC}"
    fi
else
    echo "   No credentials to restore"
fi

# Import workflows
if [ -d "$RESTORE_PATH/workflows" ]; then
    echo "🔄 Restoring workflows..."
    if n8n import:workflow --separate --input="$RESTORE_PATH/workflows"; then
        echo -e "${GREEN}✅ Workflows restored successfully${NC}"
    else
        echo -e "${RED}❌ Workflow restore failed${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ No workflows found in backup${NC}"
    exit 1
fi

# Activate workflows
echo "▶️ Activating restored workflows..."
n8n update:workflow --all --active=true || echo "   Some workflows may not have been activated"

# Count restored items
WORKFLOW_COUNT=$(find "$RESTORE_PATH/workflows" -name "*.json" 2>/dev/null | wc -l || echo "0")
CREDENTIAL_COUNT=$(find "$RESTORE_PATH/credentials" -name "*.json" 2>/dev/null | wc -l || echo "0")

echo -e "${GREEN}🎉 Restore complete!${NC}"
echo ""
echo "Restore Summary:"
echo "  • Workflows restored: $WORKFLOW_COUNT"
echo "  • Credentials restored: $CREDENTIAL_COUNT"
echo "  • All workflows activated"
echo ""
echo "Access n8n at: http://localhost:5678"
