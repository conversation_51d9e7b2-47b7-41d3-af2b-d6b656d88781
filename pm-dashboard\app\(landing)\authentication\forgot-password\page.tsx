"use client";

import { <PERSON><PERSON>, Card, Checkbox, Label, TextInput } from "flowbite-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  
  const { resetPassword } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (!email) {
      setError("Email is required");
      return;
    }

    setLoading(true);

    try {
      const { data, error } = await resetPassword(email);
      
      if (error) {
        setError(error.message);
      } else {
        setSuccess("Password reset email sent! Please check your inbox.");
      }
    } catch (err) {
      setError("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="mx-auto flex flex-col items-center justify-center px-6 pt-8 md:h-screen">
      <Link
        href="/"
        className="mb-8 flex items-center justify-center text-2xl font-semibold lg:mb-10 dark:text-white"
      >
        <span className="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">
          Pulse
        </span>
      </Link>
      <Card
        className="sm:max-w-(--breakpoint-sm) md:max-w-(--breakpoint-sm)"
        theme={{ root: { children: "w-full p-6 sm:p-8 md:p-16" } }}
      >
        <h2 className="mb-3 text-2xl font-bold text-gray-900 lg:text-3xl dark:text-white">
          Forgot your password?
        </h2>
        <p className="text-base font-normal text-gray-500 dark:text-gray-400">
          Don&apos;t fret! Just type in your email and we will send you a code
          to reset your password!
        </p>
        
        {error && (
          <div className="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400">
            {error}
          </div>
        )}
        
        {success && (
          <div className="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400">
            {success}
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="mb-6 flex flex-col gap-y-2">
            <Label htmlFor="email">Your email</Label>
            <TextInput
              id="email"
              name="email"
              placeholder="<EMAIL>"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <div>
            <Button
              size="lg"
              color="blue"
              type="submit"
              className="w-full sm:w-auto"
              disabled={loading}
            >
              {loading ? "Sending..." : "Reset password"}
            </Button>
          </div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Remember your password?&nbsp;
            <Link
              href="/authentication/sign-in"
              className="text-primary-700 dark:text-primary-500 hover:underline"
            >
              Sign in here
            </Link>
          </p>
        </form>
      </Card>
    </div>
  );
}
