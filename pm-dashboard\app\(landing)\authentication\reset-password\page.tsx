import { <PERSON><PERSON>, Card, Checkbox, Label, TextInput } from "flowbite-react";
import Image from "next/image";
import Link from "next/link";

export default function ResetPasswordPage() {
  return (
    <div className="mx-auto flex flex-col items-center justify-center px-6 pt-8 md:h-screen">
      <Link
        href="/"
        className="mb-8 flex items-center justify-center text-2xl font-semibold lg:mb-10 dark:text-white"
      >
        <Image
          alt=""
          src="/images/logo.svg"
          width={43}
          height={44}
          className="mr-4 h-11"
        />
        <span className="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">
          Flowbite
        </span>
      </Link>
      <Card
        horizontal
        imgAlt=""
        imgSrc="/images/authentication/reset-password.jpg"
        className="w-full md:max-w-(--breakpoint-lg)"
        theme={{
          root: {
            children: "my-auto w-full gap-0 space-y-8 p-6 sm:p-8 lg:p-16",
          },
          img: {
            horizontal: {
              on: "hidden w-2/3 rounded-l-lg md:w-96 md:p-0 lg:block",
            },
          },
        }}
      >
        <h2 className="text-2xl font-bold text-gray-900 lg:text-3xl dark:text-white">
          Reset your password
        </h2>
        <form className="mt-8 space-y-6">
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="email">Your email</Label>
            <TextInput
              id="email"
              name="email"
              placeholder="<EMAIL>"
              type="email"
            />
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="newPassword">New password</Label>
            <TextInput
              id="newPassword"
              name="newPassword"
              placeholder="••••••••"
              type="password"
            />
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="confirmNewPassword">Confirm New Password</Label>
            <TextInput
              id="confirmNewPassword"
              name="confirmNewPassword"
              placeholder="••••••••"
              type="password"
            />
          </div>
          <div className="flex items-center gap-x-3">
            <Checkbox id="acceptTerms" name="acceptTerms" />
            <Label htmlFor="acceptTerms">
              I accept the&nbsp;
              <Link
                href="#"
                className="text-primary-700 dark:text-primary-500 hover:underline"
              >
                Terms and Conditions
              </Link>
            </Label>
          </div>
          <div>
            <Button
              size="lg"
              color="blue"
              type="submit"
              className="w-full sm:w-auto"
            >
              Reset password
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
