# syntax=docker/dockerfile:1
# Pulse Project Management Dashboard
# Multi-stage build for Next.js with Bun

ARG BUN_VERSION=1.2.14

# Stage 1: Dependencies
FROM oven/bun:${BUN_VERSION}-alpine AS deps
WORKDIR /app

# Copy package files first for better layer caching
COPY package.json bun.lock* ./

# Install dependencies with cache mount for better performance
RUN --mount=type=cache,target=/root/.bun/install/cache \
    bun install --frozen-lockfile --production=false

# Stage 2: Builder
FROM oven/bun:${BUN_VERSION}-alpine AS builder
WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code (excluding files in .dockerignore)
COPY . .

# Set environment variables for build
ENV NEXT_TELEMETRY_DISABLED=1 \
    NODE_ENV=production \
    SKIP_ENV_VALIDATION=1

# Build the application with optimizations
RUN --mount=type=cache,target=/app/.next/cache \
    ESLINT_NO_DEV_ERRORS=true bun run build

# Stage 3: Runner
FROM oven/bun:${BUN_VERSION}-alpine AS runner
WORKDIR /app

# Set environment variables
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1 \
    PORT=3000 \
    HOSTNAME="0.0.0.0"

# Create non-root user with proper security
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 --ingroup nodejs nextjs

# Copy built application with proper ownership
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Add healthcheck using wget (already available in Alpine)
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# Start the application
CMD ["bun", "server.js"]
