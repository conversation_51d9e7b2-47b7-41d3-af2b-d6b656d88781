# Pulse Management Scripts

Modern Bun-powered scripts for cross-platform development. All scripts are optimized for speed and simplicity.

| Script | Description |
|--------|-------------|
| **setup.js** | Complete environment validation and first-time setup. Checks Node.js, Docker, Bun, creates `.env`, installs dependencies. |
| **dev.js** | Smart development environment startup with service orchestration, hot-reload, and live log monitoring. |
| **seed.js** | Intelligent database and workflow seeding with duplicate detection. Imports demo users, projects, and n8n workflows. |
| **test.js** | Comprehensive health checks: services, database, authentication, API gateway, n8n workflows, and dashboard connectivity. |
| **reset.js** | Selective reset capabilities with scope options (database, dashboard, n8n, volumes, or complete reset). |

---

## Usage

```bash
# Essential workflow
bun setup      # First-time environment setup
bun dev        # Start development with hot-reload  
bun seed       # Add demo data and workflows
bun test       # Comprehensive health checks
bun reset      # Clean restart when needed

# Quality & building
bun build      # Production build
bun lint       # Code quality checks
bun typecheck  # TypeScript validation
```

## Script Features

- **Cross-platform**: Works on Windows, macOS, and Linux via Bun
- **Fast execution**: 3-5x faster than equivalent Node.js scripts
- **Smart detection**: Automatically handles first-time vs. repeat runs
- **Comprehensive validation**: Built-in environment and service checks
- **Selective operations**: Reset specific components without affecting others
- **Real-time feedback**: Color-coded output with progress indicators
