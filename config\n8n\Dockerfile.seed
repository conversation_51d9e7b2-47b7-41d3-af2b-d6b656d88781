# n8n workflow seeding container
FROM n8nio/n8n:latest

# Install additional tools
USER root
RUN apk add --no-cache \
    bash \
    curl \
    jq \
    && rm -rf /var/cache/apk/*

# Switch back to n8n user
USER node

# Create seed directory
WORKDIR /seed

# Copy seeding scripts
COPY import-workflows.sh /usr/local/bin/
COPY demo-data/ /seed/demo-data/

# Make scripts executable
USER root
RUN chmod +x /usr/local/bin/*.sh
USER node

# Set default command
CMD ["import-workflows.sh"]
