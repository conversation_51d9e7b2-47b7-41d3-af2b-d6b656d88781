{"permissions": {"allow": ["Bash(find:*)", "Bash(cp:*)", "Bash(rm:*)", "Bash(ls:*)", "<PERSON><PERSON>(docker-compose restart:*)", "Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker-compose build:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(bun reset:*)", "mcp__Ref__ref_search_documentation", "mcp__filesystem-mcp-server__read_file", "mcp__filesystem-mcp-server__set_filesystem_default", "mcp__terminal-controller__get_current_directory", "mcp__terminal-controller__change_directory", "mcp__terminal-controller__list_directory", "mcp__filesystem-mcp-server__list_files", "mcp__filesystem-mcp-server__write_file", "mcp__filesystem-mcp-server__update_file", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(docker compose:*)"], "deny": []}, "enableAllProjectMcpServers": true}