#!/usr/bin/env bun

/**
 * Pulse Setup Script
 * Modern setup using Bun for cross-platform compatibility
 * Replaces: validate.js, quick-setup.js, validate-setup*.ps1/sh
 */

import { spawn } from "bun";
import { existsSync, copyFileSync } from "fs";
import { join } from "path";

console.log("🚀 Pulse - AI Project Management Platform Setup");
console.log("=" .repeat(50));

// Color output utilities
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

// Helper function to run command with proper Windows PATH
async function runCommand(command, args = []) {
  try {
    // On Windows, ensure we try both the command as-is and with .exe/.cmd extensions
    const isWindows = process.platform === 'win32';
    const commandVariants = isWindows ? [command, `${command}.exe`, `${command}.cmd`] : [command];
    
    let lastError;
    
    for (const cmd of commandVariants) {
      try {
        const proc = spawn({
          cmd: [cmd, ...args],
          stdout: "pipe",
          stderr: "pipe",
          env: {
            ...process.env,
            // Ensure PATH is properly inherited on Windows
            PATH: process.env.PATH || process.env.Path || ''
          },
        });
        
        const output = await new Response(proc.stdout).text();
        const result = await proc.exited;
        
        if (result === 0) {
          return output.trim();
        } else {
          throw new Error(`Command failed with exit code ${result}`);
        }
      } catch (error) {
        lastError = error;
        continue; // Try next variant
      }
    }
    
    throw lastError;
  } catch (error) {
    throw new Error(`Command '${command}' not found or failed: ${error.message}`);
  }
}

// Check requirements
async function checkRequirements() {
  console.log("\n📋 Checking system requirements...\n");
  
  // Debug: Show runtime information
  if (process.env.DEBUG) {
    console.log(`${colors.yellow("🔍 Debug Info:")}`);
    console.log(`   Platform: ${process.platform}`);
    console.log(`   Runtime: ${process.versions.bun ? 'Bun' : 'Node'} ${process.versions.bun || process.versions.node}`);
    console.log(`   PATH: ${process.env.PATH?.substring(0, 100)}...`);
    console.log();
  }
  
  const checks = [
    {
      name: "Node.js (>=18)",
      command: "node",
      args: ["--version"],
      test: (output) => {
        const version = parseInt(output.match(/v(\d+)/)?.[1] || "0");
        return version >= 18;
      }
    },
    {
      name: "Docker",
      command: "docker",
      args: ["--version"],
      test: (output) => output.includes("Docker version")
    },
    {
      name: "Docker Compose",
      command: "docker",
      args: ["compose", "version"],
      test: (output) => output.includes("Docker Compose version") || output.includes("version v")
    },
    {
      name: "Bun",
      command: "bun",
      args: ["--version"],
      test: (output) => /^\d+\.\d+\.\d+/.test(output.trim()),
      skipIfRunningWithBun: true
    }
  ];

  let allPassed = true;
  const diagnostics = [];

  for (const check of checks) {
    // Special case: if we're running with Bun and this is the Bun check, assume it's working
    if (check.skipIfRunningWithBun && (process.versions.bun || process.isBun || typeof Bun !== 'undefined')) {
      const bunVersion = process.versions.bun || 'detected';
      console.log(`✅ ${colors.green(check.name)} - ${bunVersion} (detected from runtime)`);
      continue;
    }
    
    try {
      const result = await runCommand(check.command, check.args);
      const passed = check.test(result);
      
      if (passed) {
        console.log(`✅ ${colors.green(check.name)} - ${result}`);
      } else {
        console.log(`❌ ${colors.red(check.name)} - Invalid version: ${result}`);
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${colors.red(check.name)} - Not found`);
      diagnostics.push(`${check.name}: ${error.message}`);
      allPassed = false;
    }
  }

  if (!allPassed) {
    console.log(`\n${colors.red("❌ Some requirements are missing or not in PATH:")}`);
    console.log("• Node.js 18+ from https://nodejs.org");
    console.log("• Docker Desktop from https://docker.com");
    console.log("• Bun from https://bun.sh");
    
    console.log(`\n${colors.yellow("🔍 Diagnostics:")}`);
    diagnostics.forEach(diag => console.log(`   ${diag}`));
    
    console.log(`\n${colors.cyan("💡 Troubleshooting:")}`);
    console.log("• Make sure Docker Desktop is running");
    console.log("• Restart your terminal after installing tools");
    console.log("• Check that tools are added to your PATH");
    console.log("• Try running commands manually: 'node --version', 'docker --version'");
    
    process.exit(1);
  }

  console.log(`\n${colors.green("✅ All system requirements met!")}`);
}

// Setup environment file
async function setupEnvironment() {
  console.log("\n🔧 Setting up environment configuration...\n");
  
  const envPath = ".env";
  const envExamplePath = ".env.example";
  
  if (!existsSync(envPath)) {
    if (existsSync(envExamplePath)) {
      copyFileSync(envExamplePath, envPath);
      console.log(`✅ ${colors.green("Created .env from .env.example")}`);
      console.log(`${colors.yellow("⚠️  Please review and update .env with your specific values")}`);
    } else {
      console.log(`❌ ${colors.red(".env.example not found")}`);
      process.exit(1);
    }
  } else {
    console.log(`✅ ${colors.green(".env file already exists")}`);
  }
}

// Check Docker status
async function checkDocker() {
  console.log("\n🐳 Checking Docker status...\n");
  
  try {
    await runCommand("docker", ["info"]);
    console.log(`✅ ${colors.green("Docker is running")}`);
  } catch (error) {
    console.log(`❌ ${colors.red("Docker is not running")}`);
    console.log("Please start Docker Desktop and try again");
    process.exit(1);
  }
}

// Validate dashboard structure
async function validateDashboard() {
  console.log("\n📦 Validating dashboard structure...\n");
  
  const dashboardPath = join(process.cwd(), "pm-dashboard");
  const packageJsonPath = join(dashboardPath, "package.json");
  
  if (!existsSync(dashboardPath)) {
    console.log(`❌ ${colors.red("Dashboard directory not found")}`);
    process.exit(1);
  }
  
  if (!existsSync(packageJsonPath)) {
    console.log(`❌ ${colors.red("Dashboard package.json not found")}`);
    process.exit(1);
  }
  
  console.log(`✅ ${colors.green("Dashboard structure validated")}`);
  console.log(`${colors.cyan("Dependencies will be installed inside Docker container during build")}`);
}

// Manual verification for when auto-detection fails
async function manualVerification() {
  console.log(`\n${colors.yellow("🤔 Auto-detection failed. Let's try manual verification...")}`);
  console.log("\nPlease test these commands manually in your terminal:");
  console.log(`• ${colors.cyan("node --version")} (should show v18+ or v20+)`);
  console.log(`• ${colors.cyan("docker --version")} (should show Docker version)`);
  console.log(`• ${colors.cyan("docker compose version")} (should show Docker Compose version)`);
  console.log(`• ${colors.cyan("bun --version")} (should show version number)`);
  
  console.log(`\n${colors.yellow("If all commands work, you can:")}`);
  console.log(`• Run ${colors.cyan("bun setup --skip-checks")} to bypass validation`);
  console.log(`• Or continue setup manually with ${colors.cyan("bun dev")}`);
}

// Main setup flow
async function main() {
  const skipChecks = process.argv.includes('--skip-checks');
  
  try {
    if (!skipChecks) {
      await checkRequirements();
    } else {
      console.log(`${colors.yellow("⚠️  Skipping system requirement checks")}`);
    }
    
    await setupEnvironment();
    
    if (!skipChecks) {
      await checkDocker();
    } else {
      console.log(`${colors.yellow("⚠️  Skipping Docker check - make sure Docker Desktop is running")}`);
    }
    
    await validateDashboard();
    
    console.log(`\n${colors.green("🎉 Setup completed successfully!")}`);
    console.log("\nNext steps:");
    console.log(`• Run ${colors.cyan("bun dev")} to start development environment`);
    console.log(`• Run ${colors.cyan("bun seed")} to add demo data (optional)`);
    console.log(`• Visit ${colors.blue("http://localhost:3000")} when ready`);
    
  } catch (error) {
    console.log(`\n${colors.red("❌ Setup failed:")}`);
    console.log(error.message);
    
    if (!skipChecks && error.message.includes("requirement")) {
      await manualVerification();
    }
    
    process.exit(1);
  }
}

main();