############################################################################
# 🔐 SECURITY KEYS - CHANGE THESE BEFORE PRODUCTION!
############################################################################
# Generate new keys: https://supabase.com/docs/guides/self-hosting/docker#generate-api-keys

POSTGRES_PASSWORD=your-super-secret-and-long-postgres-password
JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long
ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6InN1cGFiYXNlIiwiaWF0IjoxNzUxMDg2ODAwLCJleHAiOjE5MDg4NTMyMDB9.ZvEDn3ykbm4EzNHfU5SG7exKPD1HkK9w_760ZJa63OQ
SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NTEwODY4MDAsImV4cCI6MTkwODg1MzIwMH0.EPcjEp1sYxQLrdOFr-yyX9viqjK-1-dwvfFzHJdVd9I
DASHBOARD_USERNAME=supabase
DASHBOARD_PASSWORD=this_password_is_insecure_and_should_be_updated
SECRET_KEY_BASE=UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8etQoXz3gZv1/u2oq
VAULT_ENC_KEY=your-encryption-key-32-chars-min


############################################################################
# 🗄️ DATABASE CONFIGURATION
############################################################################

POSTGRES_HOST=db
POSTGRES_DB=postgres
POSTGRES_PORT=5432
POSTGRES_USER=postgres

# Connection Pooling (Supavisor)
POOLER_PROXY_PORT_TRANSACTION=6543
POOLER_DEFAULT_POOL_SIZE=20
POOLER_MAX_CLIENT_CONN=100
POOLER_TENANT_ID=pulse
POOLER_DB_POOL_SIZE=5

############################################################################
# 🌐 API GATEWAY & SERVICES
############################################################################

# Kong API Gateway
KONG_HTTP_PORT=8000
KONG_HTTPS_PORT=8443

# PostgREST API
PGRST_DB_SCHEMAS=public,storage,graphql_public,n8n


############################################################################
# 🔐 AUTHENTICATION (Supabase Auth)
############################################################################

# General Settings
SITE_URL=http://localhost:3000
ADDITIONAL_REDIRECT_URLS=
JWT_EXPIRY=3600
DISABLE_SIGNUP=false
API_EXTERNAL_URL=http://localhost:8000

# Email Authentication
ENABLE_EMAIL_SIGNUP=true
ENABLE_EMAIL_AUTOCONFIRM=false
ENABLE_ANONYMOUS_USERS=false

# Phone Authentication  
ENABLE_PHONE_SIGNUP=true
ENABLE_PHONE_AUTOCONFIRM=true

# Email Configuration (Development - uses Inbucket)
SMTP_ADMIN_EMAIL=<EMAIL>
SMTP_HOST=inbucket
SMTP_PORT=2500
SMTP_USER=fake_mail_user
SMTP_PASS=fake_mail_password
SMTP_SENDER_NAME=Pulse Platform

# Email URL Paths
MAILER_URLPATHS_CONFIRMATION="/auth/v1/verify"
MAILER_URLPATHS_INVITE="/auth/v1/verify"
MAILER_URLPATHS_RECOVERY="/auth/v1/verify"
MAILER_URLPATHS_EMAIL_CHANGE="/auth/v1/verify"


############################################################################
# 🎨 DASHBOARD CONFIGURATION
############################################################################

DASHBOARD_PORT=3000
SUPABASE_PUBLIC_URL=http://localhost:8000

############################################################################
# 🔧 BACKEND SERVICES
############################################################################

# Studio Administration (Internal)
STUDIO_DEFAULT_ORGANIZATION=Default Organization
STUDIO_DEFAULT_PROJECT=Default Project

# Image Processing
IMGPROXY_ENABLE_WEBP_DETECTION=true

# Edge Functions
FUNCTIONS_VERIFY_JWT=false

# Analytics & Logging
LOGFLARE_PUBLIC_ACCESS_TOKEN=your-super-secret-and-long-logflare-key-public
LOGFLARE_PRIVATE_ACCESS_TOKEN=your-super-secret-and-long-logflare-key-private
DOCKER_SOCKET_LOCATION=/var/run/docker.sock

############################################################################
# 🤖 AI & AUTOMATION
############################################################################

# n8n Workflow Platform
N8N_ENCRYPTION_KEY=super-secret-key
N8N_USER_MANAGEMENT_JWT_SECRET=even-more-secret
N8N_DEFAULT_BINARY_DATA_MODE=filesystem
N8N_ADMIN_EMAIL=<EMAIL>
N8N_ADMIN_PASSWORD=pulse123!

# AI Service Keys (Optional - add your keys)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
PERPLEXITY_API_KEY=



# Ollama Local AI (Optional)
OLLAMA_HOST=0.0.0.0
OLLAMA_ORIGINS=*
OLLAMA_BASE_URL=http://ollama:11434
OLLAMA_DEFAULT_MODELS=llama3.2:1b,nomic-embed-text

############################################################################
# 🚀 PRODUCTION DEPLOYMENT (Optional)
############################################################################

DOMAIN=pulse.dev
ACME_EMAIL=<EMAIL>
GITHUB_REPOSITORY=your-org/pulse
IMAGE_TAG=latest
