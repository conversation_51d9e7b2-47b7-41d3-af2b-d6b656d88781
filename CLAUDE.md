# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Pulse** is an AI-powered project management platform built with modern containerized architecture. It combines a Next.js 15 dashboard with Supabase backend services, n8n workflow automation, and PostgreSQL with pgvector extensions for AI capabilities. The project has been optimized for developer productivity using Bun and modern tooling.

## Quick Start Commands

### Essential Development Commands
```bash
# First-time setup (validates environment, installs dependencies)
bun setup

# Start development environment with hot reload
bun dev

# Add demo data and workflows
bun seed

# Run comprehensive health checks and tests
bun test

# Clean reset and restart
bun reset
```

### Code Quality & Building
```bash
# Production build
bun build

# Code quality checks
bun lint

# TypeScript validation
bun typecheck

# View service logs
bun logs
```

### Production Deployment
```bash
# Start production environment
bun prod
```

## Architecture Overview

### Technology Stack
- **Frontend**: Next.js 15.3.3 with App Router, Turbopack, Flowbite React 0.11.7
- **Styling**: Tailwind CSS 4.1.7 with optimized configuration
- **Backend**: Supabase self-hosted (PostgreSQL + Auth + Storage + Edge Functions)
- **API Gateway**: Kong 2.8.1 for routing and security
- **Automation**: n8n for visual workflow automation
- **AI/Vector Search**: PostgreSQL 15.8.1 with pgvector extension
- **Local AI**: Ollama for optional local inference
- **Package Manager**: Bun 1.2.14 for fast development
- **Container Orchestration**: Docker Compose with simplified configuration

### Service Architecture & Ports

| Service | URL | Purpose |
|---------|-----|---------|
| Dashboard | http://localhost:3000 | Next.js application with hot-reload |
| Kong API Gateway | http://localhost:8000 | API routing and security |
| n8n Workflows | http://localhost:5678 | Visual automation platform |
| Email Testing | http://localhost:9000 | Inbucket for development emails |
| Database | localhost:5432 | PostgreSQL with extensions |
| PostgREST API | localhost:3001 | Auto-generated REST API |

## Modern Development Workflow

### Bun-Powered Scripts
The project uses 5 core Bun scripts instead of the previous 25+ platform-specific scripts:

- **`scripts/setup.js`** - Environment validation and first-time setup
- **`scripts/dev.js`** - Development environment with smart service orchestration
- **`scripts/seed.js`** - Database and workflow seeding with smart detection
- **`scripts/test.js`** - Comprehensive health checks and integration tests
- **`scripts/reset.js`** - Selective reset with scope options

### Frontend Development (pm-dashboard/)

#### Key Technologies
- **Next.js 15.3.3**: Uses App Router with optimized standalone builds
- **Turbopack**: Enabled for fast development builds
- **Flowbite React**: Component library with dark mode support
- **Tailwind CSS 4.1.7**: Modern utility-first styling
- **TypeScript 5.8.3**: Full type safety
- **Bun**: Fast package management and script execution

#### Development Features
- Hot-reload with Docker Compose Watch mode
- Optimized for Docker development environment
- Cross-platform file watching
- Automatic dependency installation

#### Frontend Commands
```bash
cd pm-dashboard

# Install dependencies
bun install

# Development with Turbopack
bun dev

# Production build
bun build

# Code quality
bun lint && bun typecheck

# Format code
bun format
```

## Database & Backend Architecture

### PostgreSQL with Extensions
- **pgvector**: Vector similarity search for AI embeddings
- **PostgREST**: Auto-generated REST API from database schema
- **Row Level Security**: Multi-tenant data isolation
- **Real-time subscriptions**: WebSocket support via Supabase Realtime

### Key Schemas
- `public`: Main application tables
- `auth`: Supabase authentication (managed)
- `storage`: File storage metadata (managed)
- `n8n`: Workflow automation data
- `_analytics`: System analytics and logs

### Authentication System
- **Supabase Auth**: JWT-based authentication with email/phone support
- **Email confirmation**: Configurable (auto-confirm for development)
- **Row Level Security**: Database-level authorization
- **Session management**: Handled automatically by Supabase client

## AI & Automation Features

### n8n Workflow Platform
- **Visual automation**: Drag-and-drop workflow builder at localhost:5678
- **Database integration**: Direct PostgreSQL connections
- **AI integrations**: Pre-configured for OpenAI, Anthropic, and other providers
- **Webhook endpoints**: Real-time event processing
- **Demo workflows**: Automatically imported during seeding

### Vector Search with pgvector
```sql
-- Example: Find similar documents using vector embeddings
SELECT content, 
       1 - (embedding <=> $1::vector) as similarity
FROM documents
ORDER BY embedding <=> $1::vector
LIMIT 5;
```

### Edge Functions (Supabase)
Located in `volumes/functions/`:
- **main/**: Function router with JWT validation at `volumes/functions/main/index.ts:86`
- **hello/**: Example function template

## Environment Configuration

### Key Environment Variables
The `.env.example` file is organized into clear sections:

```bash
# Security (MUST change for production)
POSTGRES_PASSWORD=your-secure-password
JWT_SECRET=your-jwt-secret-32-chars-min

# Database
POSTGRES_HOST=db
POSTGRES_PORT=5432

# API Gateway
KONG_HTTP_PORT=8000
API_EXTERNAL_URL=http://localhost:8000

# Authentication
SITE_URL=http://localhost:3000
ENABLE_EMAIL_AUTOCONFIRM=false  # Set true for dev

# n8n Automation
N8N_ADMIN_EMAIL=<EMAIL>
N8N_ADMIN_PASSWORD=pulse123!

# AI Services (optional)
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
```

## Testing & Validation

### Comprehensive Testing
The test script validates:
- Docker service health
- Database connectivity and extensions
- API gateway routing
- Authentication flow
- n8n workflow status
- Dashboard responsiveness

### Demo Data
Default demo accounts (password: `demo123!`):
- `<EMAIL>` - Administrator role
- `<EMAIL>` - Project Manager role
- `<EMAIL>` - Developer role

## Development Best Practices

### Code Organization
```
├── pm-dashboard/              # Next.js frontend
│   ├── app/                   # App Router pages and layouts
│   ├── components/            # Reusable React components
│   ├── contexts/              # React Context providers
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utility libraries (Supabase client)
│   └── types/                 # TypeScript definitions
├── scripts/                   # Bun-powered management scripts
├── volumes/                   # Docker persistent data
│   ├── db/                    # Database initialization SQL
│   ├── functions/             # Supabase Edge Functions
│   └── n8n/                   # n8n workflow data
└── config/                    # Service configurations
```

### Script Usage Patterns
```bash
# Development cycle
bun setup    # Once for new environment
bun dev      # Daily development
bun seed     # When you need demo data
bun test     # Before commits/deploys
bun reset    # When things go wrong

# Selective operations
bun reset --scope=database    # Reset only database
bun seed --force             # Re-seed existing data
bun test                     # Full health check
```

## Docker Configuration

### Simplified Architecture
The docker-compose.yml has been streamlined from 950+ lines to ~400 lines:
- Removed complex profile system (only `dev` and `prod` profiles remain)
- Eliminated utility containers for init/seed/reset operations
- Simplified service dependencies and health checks
- Modern Docker Compose syntax with Watch mode

### Service Dependencies
```
Database (PostgreSQL + extensions)
├── Vector logging
├── Analytics
├── Authentication
├── REST API
├── Realtime
├── Storage
├── Functions
└── Kong Gateway
    └── Dashboard (Next.js)
    └── n8n Automation
```

## Troubleshooting

### Common Issues

**Services won't start:**
```bash
bun test    # Check service health
bun reset   # Clean restart if needed
```

**Database connection issues:**
```bash
bun logs | grep db    # Check database logs
bun db:connect        # Direct database connection
```

**Authentication problems:**
```bash
# Check if email confirmation is required
# Visit http://localhost:9000 for development emails
```

**n8n workflows not working:**
```bash
bun logs | grep n8n   # Check n8n logs
# Visit http://localhost:5678 to manage workflows
```

### Reset Options
```bash
bun reset                        # Full reset
bun reset --scope=database       # Database only
bun reset --scope=dashboard      # Dashboard only
bun reset --preserve-data        # Keep volumes
```

## Security Considerations

### Development vs Production
- **Development**: Email auto-confirmation enabled, Inbucket for testing
- **Production**: External SMTP required, email confirmation enforced
- **Security keys**: Must generate new JWT_SECRET and API keys for production

### Docker Security
- Container security options enabled in production
- Read-only containers where appropriate
- Proper network isolation between services

## Performance Optimizations

### Next.js 15.3.3 Features
- **Turbopack**: Stable fast builds for development
- **Standalone output**: Optimized production deployments
- **App Router**: Server-side rendering and streaming
- **Optimized package imports**: Tree-shaking for Flowbite and Supabase

### Bun Performance Benefits
- 3-5x faster script execution compared to Node.js
- Native cross-platform compatibility
- Fast package installation and management
- Built-in testing and bundling capabilities

### Database Optimizations
- **pgvector**: Optimized for AI embeddings and similarity search
- **Connection pooling**: Supavisor for high concurrency
- **Proper indexing**: Database indexes for common queries

## Key Files for Development

### Configuration Files
- `docker-compose.yml`: Simplified service orchestration (400 lines vs 950)
- `pm-dashboard/next.config.mjs`: Next.js 15 optimized configuration
- `pm-dashboard/package.json`: Bun-optimized frontend dependencies
- `.env.example`: Organized environment template

### Entry Points
- `pm-dashboard/app/layout.tsx`: Root layout with providers
- `pm-dashboard/lib/supabase.ts`: Supabase client at `pm-dashboard/lib/supabase.ts:6`
- `scripts/dev.js`: Main development orchestration script
- `scripts/setup.js`: Environment validation and setup

### Database
- `volumes/db/*.sql`: Database initialization and migrations
- `volumes/functions/main/index.ts`: Edge Functions router

This platform provides a complete foundation for AI-enhanced project management with modern development practices, simplified tooling, and comprehensive automation capabilities.