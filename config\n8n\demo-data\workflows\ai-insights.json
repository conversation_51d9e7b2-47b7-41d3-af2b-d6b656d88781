{"name": "AI Project Insights", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "id": "schedule-trigger", "name": "Every 6 Hours", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [240, 300]}, {"parameters": {"url": "http://dashboard:3000/api/projects/active", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}"}}}, "id": "get-active-projects", "name": "Get Active Projects", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"url": "http://dashboard:3000/api/ai/analyze-project", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}", "Content-Type": "application/json"}}, "jsonParameters": true, "bodyParametersJson": "={\n  \"project_id\": \"{{$json.id}}\",\n  \"analysis_type\": \"comprehensive\",\n  \"include_predictions\": true\n}"}, "id": "analyze-project", "name": "AI Project Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"conditions": {"number": [{"value1": "={{$json.risk_score}}", "operation": "larger", "value2": 0.7}]}}, "id": "high-risk-filter", "name": "High Risk Filter", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"url": "http://dashboard:3000/api/notifications/send", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}", "Content-Type": "application/json"}}, "jsonParameters": true, "bodyParametersJson": "={\n  \"type\": \"risk_alert\",\n  \"recipient\": \"{{$json.project_owner}}\",\n  \"data\": {\n    \"project_name\": \"{{$json.project_name}}\",\n    \"risk_score\": \"{{$json.risk_score}}\",\n    \"risk_factors\": \"{{$json.risk_factors}}\",\n    \"recommendations\": \"{{$json.recommendations}}\"\n  }\n}"}, "id": "send-risk-alert", "name": "Send Risk Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 200]}, {"parameters": {"url": "http://dashboard:3000/api/insights/store", "options": {"headers": {"Authorization": "Bearer {{$env.SUPABASE_SERVICE_ROLE_KEY}}", "Content-Type": "application/json"}}, "jsonParameters": true, "bodyParametersJson": "={\n  \"project_id\": \"{{$json.project_id}}\",\n  \"insight_type\": \"ai_analysis\",\n  \"data\": {\n    \"risk_score\": \"{{$json.risk_score}}\",\n    \"completion_prediction\": \"{{$json.completion_prediction}}\",\n    \"resource_optimization\": \"{{$json.resource_optimization}}\",\n    \"generated_at\": \"{{$now.toISO()}}\"\n  }\n}"}, "id": "store-insights", "name": "Store AI Insights", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 400]}], "connections": {"Every 6 Hours": {"main": [[{"node": "Get Active Projects", "type": "main", "index": 0}]]}, "Get Active Projects": {"main": [[{"node": "AI Project Analysis", "type": "main", "index": 0}]]}, "AI Project Analysis": {"main": [[{"node": "High Risk Filter", "type": "main", "index": 0}, {"node": "Store AI Insights", "type": "main", "index": 0}]]}, "High Risk Filter": {"main": [[{"node": "Send Risk Alert", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "UTC"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "ai-project-insights", "tags": []}