name: CI

on:
  pull_request:
    branches:
      - main

jobs:
  format:
    name: 💅 Format
    runs-on: ubuntu-latest
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run format
        run: bun run format:check

  lint:
    name: 🕵 Lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run lint
        run: bun run lint

  typecheck:
    name: ✅ Typecheck
    runs-on: ubuntu-latest
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run typecheck
        run: bun run typecheck

  build:
    name: 🧰 Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run build
        run: bun run build
