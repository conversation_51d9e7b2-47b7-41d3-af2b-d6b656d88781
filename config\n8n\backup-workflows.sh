#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}💾 n8n Workflow Backup${NC}"
echo "======================="

# Configuration
N8N_URL="http://n8n:5678"
BACKUP_DIR="/seed/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_PATH="$BACKUP_DIR/n8n_backup_$TIMESTAMP"

# Create backup directory
mkdir -p "$BACKUP_PATH"

echo "📁 Backup location: $BACKUP_PATH"

# Check if n8n is accessible
echo "🔍 Checking n8n accessibility..."
if ! curl -s -f "$N8N_URL/healthz" >/dev/null 2>&1; then
    echo -e "${RED}❌ n8n is not accessible${NC}"
    exit 1
fi

echo -e "${GREEN}✅ n8n is accessible${NC}"

# Export workflows
echo "🔄 Exporting workflows..."
if n8n export:workflow --output="$BACKUP_PATH/workflows" --all; then
    echo -e "${GREEN}✅ Workflows exported successfully${NC}"
else
    echo -e "${RED}❌ Workflow export failed${NC}"
    exit 1
fi

# Export credentials
echo "🔑 Exporting credentials..."
if n8n export:credentials --output="$BACKUP_PATH/credentials" --all; then
    echo -e "${GREEN}✅ Credentials exported successfully${NC}"
else
    echo -e "${YELLOW}⚠️ Credential export failed (may not have any)${NC}"
fi

# Create backup metadata
echo "📋 Creating backup metadata..."
cat > "$BACKUP_PATH/backup_info.json" << EOF
{
  "timestamp": "$TIMESTAMP",
  "date": "$(date -Iseconds)",
  "n8n_version": "$(n8n --version 2>/dev/null || echo 'unknown')",
  "backup_type": "full",
  "files": {
    "workflows": "workflows/",
    "credentials": "credentials/"
  }
}
EOF

# Count exported items
WORKFLOW_COUNT=$(find "$BACKUP_PATH/workflows" -name "*.json" 2>/dev/null | wc -l || echo "0")
CREDENTIAL_COUNT=$(find "$BACKUP_PATH/credentials" -name "*.json" 2>/dev/null | wc -l || echo "0")

echo -e "${GREEN}🎉 Backup complete!${NC}"
echo ""
echo "Backup Summary:"
echo "  • Location: $BACKUP_PATH"
echo "  • Workflows: $WORKFLOW_COUNT"
echo "  • Credentials: $CREDENTIAL_COUNT"
echo "  • Timestamp: $TIMESTAMP"
echo ""

# Clean up old backups (keep last 5)
echo "🧹 Cleaning up old backups..."
cd "$BACKUP_DIR"
ls -t | grep "n8n_backup_" | tail -n +6 | xargs -r rm -rf
echo "   Kept last 5 backups"

echo -e "${GREEN}✅ Backup process complete${NC}"
