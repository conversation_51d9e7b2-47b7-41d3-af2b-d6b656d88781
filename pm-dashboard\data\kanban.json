[{"id": 1, "title": "To Do", "tasks": [{"id": 32, "name": "Change charts javascript", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": false, "daysLeft": 5, "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}, {"id": 23, "name": "Change homepage", "description": "Change homepage for Volt Dashboard.", "completed": false, "daysLeft": 22, "attachment": "/images/kanban/task-3.jpg", "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}]}, {"id": 2, "title": "In Progress", "tasks": [{"id": 76, "name": "Redesign tables card", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": false, "daysLeft": 9, "attachment": "/images/kanban/task-1.jpg", "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}]}, {"id": 3, "title": "Done", "tasks": [{"id": 34, "name": "Create Javascript elements", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": true, "daysLeft": 0, "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}]}, {"id": 4, "title": "", "tasks": []}]