# Simplified Pulse Project Management Docker Compose
# Optimized for modern development with Bun script management
#
# Usage:
#   bun dev        # Start development environment
#   bun prod       # Start production environment

name: pulse-project-management

networks:
  pulse-network:
    driver: bridge

# Common environment variables
x-common-variables: &common-env
  POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
  POSTGRES_HOST: ${POSTGRES_HOST}
  POSTGRES_PORT: ${POSTGRES_PORT}
  POSTGRES_DB: ${POSTGRES_DB}
  JWT_SECRET: ${JWT_SECRET}
  ANON_KEY: ${ANON_KEY}
  SERVICE_ROLE_KEY: ${SERVICE_ROLE_KEY}
  SECRET_KEY_BASE: ${SECRET_KEY_BASE}

# Standard healthcheck
x-healthcheck: &default-healthcheck
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 30s

services:
  # =============================================================================
  # CORE INFRASTRUCTURE
  # =============================================================================

  # PostgreSQL Database with Extensions
  db:
    container_name: pulse-db
    image: supabase/postgres:**********
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - ./volumes/db/realtime.sql:/docker-entrypoint-initdb.d/migrations/99-realtime.sql:Z
      - ./volumes/db/webhooks.sql:/docker-entrypoint-initdb.d/init-scripts/98-webhooks.sql:Z
      - ./volumes/db/roles.sql:/docker-entrypoint-initdb.d/init-scripts/99-roles.sql:Z
      - ./volumes/db/jwt.sql:/docker-entrypoint-initdb.d/init-scripts/99-jwt.sql:Z
      - ./volumes/db/data:/var/lib/postgresql/data:Z
      - ./volumes/db/_supabase.sql:/docker-entrypoint-initdb.d/migrations/97-_supabase.sql:Z
      - ./volumes/db/logs.sql:/docker-entrypoint-initdb.d/migrations/99-logs.sql:Z
      - ./volumes/db/pooler.sql:/docker-entrypoint-initdb.d/migrations/99-pooler.sql:Z
      - ./volumes/db/n8n.sql:/docker-entrypoint-initdb.d/migrations/99-n8n.sql:Z
      - ./volumes/db/vector.sql:/docker-entrypoint-initdb.d/migrations/99-vector.sql:Z
      - db-config:/etc/postgresql-custom
    healthcheck:
      test: ['CMD', 'pg_isready', '-U', 'postgres', '-h', 'localhost']
      <<: *default-healthcheck
    depends_on:
      vector:
        condition: service_healthy
    environment:
      <<: *common-env
      POSTGRES_HOST: /var/run/postgresql
      PGPORT: ${POSTGRES_PORT}
      PGPASSWORD: ${POSTGRES_PASSWORD}
      PGDATABASE: ${POSTGRES_DB}
      JWT_EXP: ${JWT_EXPIRY}
    command: [
      'postgres',
      '-c', 'config_file=/etc/postgresql/postgresql.conf',
      '-c', 'log_min_messages=fatal'
    ]

  # Vector Logging
  vector:
    container_name: pulse-vector
    image: timberio/vector:0.28.1-alpine
    restart: unless-stopped
    networks:
      - pulse-network
    volumes:
      - ./volumes/logs/vector.yml:/etc/vector/vector.yml:ro,z
      - /var/run/docker.sock:/var/run/docker.sock:ro,z
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://vector:9001/health']
      <<: *default-healthcheck
    environment:
      LOGFLARE_PUBLIC_ACCESS_TOKEN: ${LOGFLARE_PUBLIC_ACCESS_TOKEN}
    command: ['--config', '/etc/vector/vector.yml']

  # Analytics
  analytics:
    container_name: pulse-analytics
    image: supabase/logflare:1.14.2
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "4000:4000"
    healthcheck:
      test: ['CMD', 'curl', 'http://localhost:4000/health']
      <<: *default-healthcheck
      retries: 10
    depends_on:
      db:
        condition: service_healthy
    environment:
      LOGFLARE_NODE_HOST: 127.0.0.1
      DB_USERNAME: supabase_admin
      DB_DATABASE: _supabase
      DB_HOSTNAME: ${POSTGRES_HOST}
      DB_PORT: ${POSTGRES_PORT}
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      DB_SCHEMA: _analytics
      LOGFLARE_PUBLIC_ACCESS_TOKEN: ${LOGFLARE_PUBLIC_ACCESS_TOKEN}
      LOGFLARE_PRIVATE_ACCESS_TOKEN: ${LOGFLARE_PRIVATE_ACCESS_TOKEN}
      LOGFLARE_SINGLE_TENANT: true
      LOGFLARE_SUPABASE_MODE: true
      LOGFLARE_MIN_CLUSTER_SIZE: 1
      POSTGRES_BACKEND_URL: postgresql://supabase_admin:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/_supabase
      POSTGRES_BACKEND_SCHEMA: _analytics
      LOGFLARE_FEATURE_FLAG_OVERRIDE: multibackend=true

  # =============================================================================
  # API SERVICES
  # =============================================================================

  # Authentication Service
  auth:
    container_name: pulse-auth
    image: supabase/gotrue:v2.174.0
    restart: unless-stopped
    networks:
      - pulse-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:9999/health']
      <<: *default-healthcheck
    depends_on:
      db:
        condition: service_healthy
      analytics:
        condition: service_healthy
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: ${API_EXTERNAL_URL}
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: postgres://supabase_auth_admin:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      GOTRUE_SITE_URL: ${SITE_URL}
      GOTRUE_URI_ALLOW_LIST: ${ADDITIONAL_REDIRECT_URLS}
      GOTRUE_DISABLE_SIGNUP: ${DISABLE_SIGNUP}
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: ${JWT_EXPIRY}
      GOTRUE_JWT_SECRET: ${JWT_SECRET}
      GOTRUE_EXTERNAL_EMAIL_ENABLED: ${ENABLE_EMAIL_SIGNUP}
      GOTRUE_EXTERNAL_ANONYMOUS_USERS_ENABLED: ${ENABLE_ANONYMOUS_USERS}
      GOTRUE_MAILER_AUTOCONFIRM: ${ENABLE_EMAIL_AUTOCONFIRM}
      GOTRUE_SMTP_ADMIN_EMAIL: ${SMTP_ADMIN_EMAIL}
      GOTRUE_SMTP_HOST: ${SMTP_HOST}
      GOTRUE_SMTP_PORT: ${SMTP_PORT}
      GOTRUE_SMTP_USER: ${SMTP_USER}
      GOTRUE_SMTP_PASS: ${SMTP_PASS}
      GOTRUE_SMTP_SENDER_NAME: ${SMTP_SENDER_NAME}
      GOTRUE_MAILER_URLPATHS_INVITE: ${MAILER_URLPATHS_INVITE}
      GOTRUE_MAILER_URLPATHS_CONFIRMATION: ${MAILER_URLPATHS_CONFIRMATION}
      GOTRUE_MAILER_URLPATHS_RECOVERY: ${MAILER_URLPATHS_RECOVERY}
      GOTRUE_MAILER_URLPATHS_EMAIL_CHANGE: ${MAILER_URLPATHS_EMAIL_CHANGE}
      GOTRUE_EXTERNAL_PHONE_ENABLED: ${ENABLE_PHONE_SIGNUP}
      GOTRUE_SMS_AUTOCONFIRM: ${ENABLE_PHONE_AUTOCONFIRM}

  # REST API
  rest:
    container_name: pulse-rest
    image: postgrest/postgrest:v12.2.12
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "3001:3001"
    depends_on:
      db:
        condition: service_healthy
      analytics:
        condition: service_healthy
    environment:
      PGRST_DB_URI: postgres://authenticator:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      PGRST_DB_SCHEMAS: ${PGRST_DB_SCHEMAS}
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
      PGRST_DB_USE_LEGACY_GUCS: 'false'
      PGRST_APP_SETTINGS_JWT_SECRET: ${JWT_SECRET}
      PGRST_APP_SETTINGS_JWT_EXP: ${JWT_EXPIRY}
      PGRST_SERVER_PORT: 3001
    command: ['postgrest']

  # Realtime
  realtime:
    container_name: pulse-realtime-dev.pulse-realtime
    image: supabase/realtime:v2.34.47
    restart: unless-stopped
    networks:
      - pulse-network
    depends_on:
      db:
        condition: service_healthy
      analytics:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-sSfL', '--head', '-o', '/dev/null', '-H', 'Authorization: Bearer ${ANON_KEY}', 'http://localhost:4000/api/tenants/realtime-dev/health']
      <<: *default-healthcheck
    environment:
      PORT: 4000
      DB_HOST: ${POSTGRES_HOST}
      DB_PORT: ${POSTGRES_PORT}
      DB_USER: supabase_admin
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      DB_NAME: ${POSTGRES_DB}
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: ${JWT_SECRET}
      SECRET_KEY_BASE: ${SECRET_KEY_BASE}
      ERL_AFLAGS: -proto_dist inet_tcp
      DNS_NODES: "''"
      RLIMIT_NOFILE: '10000'
      APP_NAME: realtime
      SEED_SELF_HOST: true
      RUN_JANITOR: true

  # Storage
  storage:
    container_name: pulse-storage
    image: supabase/storage-api:v1.23.0
    restart: unless-stopped
    networks:
      - pulse-network
    volumes:
      - ./volumes/storage:/var/lib/storage:z
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://storage:5000/status']
      <<: *default-healthcheck
    depends_on:
      db:
        condition: service_healthy
      rest:
        condition: service_started
      imgproxy:
        condition: service_started
    environment:
      ANON_KEY: ${ANON_KEY}
      SERVICE_KEY: ${SERVICE_ROLE_KEY}
      POSTGREST_URL: http://rest:3001
      PGRST_JWT_SECRET: ${JWT_SECRET}
      DATABASE_URL: postgres://supabase_storage_admin:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: stub
      GLOBAL_S3_BUCKET: stub
      ENABLE_IMAGE_TRANSFORMATION: 'true'
      IMGPROXY_URL: http://imgproxy:5001

  # Image Proxy
  imgproxy:
    container_name: pulse-imgproxy
    image: darthsim/imgproxy:v3.8.0
    restart: unless-stopped
    networks:
      - pulse-network
    volumes:
      - ./volumes/storage:/var/lib/storage:z
    healthcheck:
      test: ['CMD', 'imgproxy', 'health']
      <<: *default-healthcheck
    environment:
      IMGPROXY_BIND: ':5001'
      IMGPROXY_LOCAL_FILESYSTEM_ROOT: /
      IMGPROXY_USE_ETAG: 'true'
      IMGPROXY_ENABLE_WEBP_DETECTION: ${IMGPROXY_ENABLE_WEBP_DETECTION}

  # Database Meta
  meta:
    container_name: pulse-meta
    image: supabase/postgres-meta:v0.89.3
    restart: unless-stopped
    networks:
      - pulse-network
    depends_on:
      db:
        condition: service_healthy
      analytics:
        condition: service_healthy
    environment:
      PG_META_PORT: 8080
      PG_META_DB_HOST: ${POSTGRES_HOST}
      PG_META_DB_PORT: ${POSTGRES_PORT}
      PG_META_DB_NAME: ${POSTGRES_DB}
      PG_META_DB_USER: supabase_admin
      PG_META_DB_PASSWORD: ${POSTGRES_PASSWORD}

  # Edge Functions
  functions:
    container_name: pulse-edge-functions
    image: supabase/edge-runtime:v1.67.4
    restart: unless-stopped
    networks:
      - pulse-network
    volumes:
      - ./volumes/functions:/home/<USER>/functions:Z
    depends_on:
      analytics:
        condition: service_healthy
    environment:
      JWT_SECRET: ${JWT_SECRET}
      SUPABASE_URL: http://kong:8000
      SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SERVICE_ROLE_KEY}
      SUPABASE_DB_URL: postgresql://postgres:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      VERIFY_JWT: '${FUNCTIONS_VERIFY_JWT}'
    command: ['start', '--main-service', '/home/<USER>/functions/main']

  # Connection Pooler
  supavisor:
    container_name: pulse-pooler
    image: supabase/supavisor:2.5.1
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "${POOLER_PROXY_PORT_TRANSACTION}:6543"
    volumes:
      - ./volumes/pooler/pooler.exs:/etc/pooler/pooler.exs:ro,z
    healthcheck:
      test: ['CMD', 'curl', '-sSfL', '--head', '-o', '/dev/null', 'http://127.0.0.1:4000/api/health']
      interval: 10s
      timeout: 5s
      retries: 5
    depends_on:
      db:
        condition: service_healthy
      analytics:
        condition: service_healthy
    environment:
      PORT: 4000
      POSTGRES_PORT: ${POSTGRES_PORT}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      DATABASE_URL: ecto://supabase_admin:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/_supabase
      CLUSTER_POSTGRES: true
      SECRET_KEY_BASE: ${SECRET_KEY_BASE}
      VAULT_ENC_KEY: ${VAULT_ENC_KEY}
      API_JWT_SECRET: ${JWT_SECRET}
      METRICS_JWT_SECRET: ${JWT_SECRET}
      REGION: local
      ERL_AFLAGS: -proto_dist inet_tcp
      POOLER_TENANT_ID: ${POOLER_TENANT_ID}
      POOLER_DEFAULT_POOL_SIZE: ${POOLER_DEFAULT_POOL_SIZE}
      POOLER_MAX_CLIENT_CONN: ${POOLER_MAX_CLIENT_CONN}
      POOLER_POOL_MODE: transaction
      DB_POOL_SIZE: ${POOLER_DB_POOL_SIZE}
    command: ['/bin/sh', '-c', '/app/bin/migrate && /app/bin/supavisor eval "$$(cat /etc/pooler/pooler.exs)" && /app/bin/server']

  # =============================================================================
  # API GATEWAY
  # =============================================================================

  kong:
    container_name: pulse-kong
    image: kong:2.8.1
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "${KONG_HTTP_PORT}:8000"
      - "${KONG_HTTPS_PORT}:8443"
    volumes:
      - ./volumes/api/kong.yml:/home/<USER>/temp.yml:ro,z
    depends_on:
      analytics:
        condition: service_healthy
    environment:
      KONG_DATABASE: 'off'
      KONG_DECLARATIVE_CONFIG: /home/<USER>/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
      KONG_NGINX_PROXY_PROXY_BUFFER_SIZE: 160k
      KONG_NGINX_PROXY_PROXY_BUFFERS: 64 160k
      SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_KEY: ${SERVICE_ROLE_KEY}
      DASHBOARD_USERNAME: ${DASHBOARD_USERNAME}
      DASHBOARD_PASSWORD: ${DASHBOARD_PASSWORD}
    entrypoint: bash -c 'eval "echo \"$$(cat ~/temp.yml)\"" > ~/kong.yml && /docker-entrypoint.sh kong docker-start'

  # =============================================================================
  # APPLICATIONS
  # =============================================================================

  # Development Dashboard (default)
  dashboard-dev:
    container_name: pulse-dashboard-dev
    build:
      context: ./pm-dashboard
      dockerfile: Dockerfile.dev
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_SUPABASE_URL: http://localhost:8000
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SERVICE_ROLE_KEY}
      WATCHPACK_POLLING: true
      CHOKIDAR_USEPOLLING: true
      NEXT_DEV_HOST: 0.0.0.0
      DOCKER_ENV: true
    volumes:
      - ./pm-dashboard:/app
      - /app/node_modules
      - /app/.next
    develop:
      watch:
        - action: sync
          path: ./pm-dashboard/app
          target: /app/app
          ignore:
            - node_modules/
            - .next/
        - action: sync
          path: ./pm-dashboard/components
          target: /app/components
        - action: sync
          path: ./pm-dashboard/contexts
          target: /app/contexts
        - action: sync
          path: ./pm-dashboard/hooks
          target: /app/hooks
        - action: sync
          path: ./pm-dashboard/lib
          target: /app/lib
        - action: sync
          path: ./pm-dashboard/types
          target: /app/types
        - action: rebuild
          path: ./pm-dashboard/package.json
        - action: rebuild
          path: ./pm-dashboard/next.config.mjs
    depends_on:
      kong:
        condition: service_started
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      <<: *default-healthcheck

  # Production Dashboard
  dashboard:
    profiles: ["prod"]
    container_name: pulse-dashboard
    build:
      context: ./pm-dashboard
      dockerfile: Dockerfile
      target: production
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_SUPABASE_URL: ${SUPABASE_PUBLIC_URL:-http://kong:8000}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SERVICE_ROLE_KEY}
    depends_on:
      kong:
        condition: service_started
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      <<: *default-healthcheck
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  # =============================================================================
  # AUTOMATION & AI
  # =============================================================================

  # n8n Workflow Import (runs once)
  n8n-import:
    image: n8nio/n8n:latest
    hostname: n8n-import
    container_name: pulse-n8n-import
    networks:
      - pulse-network
    environment:
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=${POSTGRES_HOST}
      - DB_POSTGRESDB_PORT=${POSTGRES_PORT}
      - DB_POSTGRESDB_DATABASE=${POSTGRES_DB}
      - DB_POSTGRESDB_SCHEMA=n8n
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      - N8N_USER_MANAGEMENT_JWT_SECRET=${N8N_USER_MANAGEMENT_JWT_SECRET}
    entrypoint: /bin/sh
    command:
      - '-c'
      - 'n8n import:credentials --separate --input=/demo-data/credentials && n8n import:workflow --separate --input=/demo-data/workflows && n8n update:workflow --all --active=true'
    volumes:
      - ./config/n8n/demo-data:/demo-data
      - ./volumes/n8n:/home/<USER>/.n8n
    depends_on:
      db:
        condition: service_healthy

  # n8n Workflow Platform
  n8n:
    image: n8nio/n8n:latest
    hostname: n8n
    container_name: pulse-n8n
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "5678:5678"
    environment:
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=${POSTGRES_HOST}
      - DB_POSTGRESDB_PORT=${POSTGRES_PORT}
      - DB_POSTGRESDB_DATABASE=${POSTGRES_DB}
      - DB_POSTGRESDB_SCHEMA=n8n
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
      - N8N_DIAGNOSTICS_ENABLED=false
      - N8N_PERSONALIZATION_ENABLED=false
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      - N8N_USER_MANAGEMENT_JWT_SECRET=${N8N_USER_MANAGEMENT_JWT_SECRET}
      - N8N_DEFAULT_BINARY_DATA_MODE=${N8N_DEFAULT_BINARY_DATA_MODE}
    volumes:
      - ./volumes/n8n:/home/<USER>/.n8n
    depends_on:
      n8n-import:
        condition: service_completed_successfully
      db:
        condition: service_healthy

  # Ollama (CPU version - most compatible)
  ollama:
    image: ollama/ollama:latest
    container_name: pulse-ollama
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "11434:11434"
    volumes:
      - ollama_storage:/root/.ollama
    environment:
      - OLLAMA_NUM_PARALLEL=4
      - OLLAMA_MAX_LOADED_MODELS=1
      - OLLAMA_CONTEXT_LENGTH=2048

  # Email Testing (development only)
  inbucket:
    image: inbucket/inbucket:latest
    container_name: pulse-email-testing
    restart: unless-stopped
    networks:
      - pulse-network
    ports:
      - "9000:9000"  # Web interface
      - "2500:2500"  # SMTP
    environment:
      INBUCKET_WEB_ADDR: 0.0.0.0:9000
      INBUCKET_SMTP_ADDR: 0.0.0.0:2500

volumes:
  db-config:
  ollama_storage: