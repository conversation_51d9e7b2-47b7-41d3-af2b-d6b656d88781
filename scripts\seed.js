#!/usr/bin/env bun

/**
 * Pulse Database & Workflow Seeding Script
 * Modern seeding using Bun for cross-platform compatibility
 * Replaces: smart-seed.bat, seed-database.bat, init-dev-data.bat, n8n import scripts
 */

import { $ } from "bun";
import { existsSync, readFileSync } from "fs";
import { join } from "path";

console.log("🌱 Pulse Database & Workflow Seeding");
console.log("=" .repeat(40));

const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

// Check if services are running
async function checkServices() {
  console.log(`${colors.blue("🔍 Checking service status...")}\n`);
  
  const requiredServices = ["db", "auth", "n8n"];
  const runningServices = [];
  
  try {
    const result = await $`docker compose ps --services --filter "status=running"`.text();
    const running = result.trim().split("\n").filter(s => s.trim());
    runningServices.push(...running);
  } catch (error) {
    console.log(`${colors.red("❌ Could not check service status")}`);
    process.exit(1);
  }
  
  const missingServices = requiredServices.filter(service => !runningServices.includes(service));
  
  if (missingServices.length > 0) {
    console.log(`${colors.yellow("⚠️  Required services not running:")}`);
    missingServices.forEach(service => console.log(`   • ${service}`));
    console.log(`\nStarting required services...`);
    
    try {
      await $`docker compose up -d ${missingServices.join(" ")}`;
      console.log(`${colors.green("✅ Services started")}`);
      
      // Wait for services to be ready
      await new Promise(resolve => setTimeout(resolve, 10000));
    } catch (error) {
      console.log(`${colors.red("❌ Failed to start services")}`);
      process.exit(1);
    }
  } else {
    console.log(`${colors.green("✅ All required services are running")}`);
  }
}

// Check if database is already seeded
async function checkDatabaseSeeded() {
  console.log(`\n${colors.blue("🔍 Checking if database is already seeded...")}`);
  
  try {
    // Check for demo users in auth.users table
    const result = await $`docker compose exec -T db psql -U postgres -d postgres -t -c "SELECT COUNT(*) FROM auth.users WHERE email LIKE '%@pulse.dev';"`.text();
    const userCount = parseInt(result.trim());
    
    if (userCount > 0) {
      console.log(`${colors.yellow("⚠️  Database already has demo data (${userCount} demo users)")}`);
      const force = process.argv.includes("--force");
      
      if (!force) {
        console.log(`Use ${colors.cyan("bun seed --force")} to re-seed anyway`);
        return true;
      } else {
        console.log(`${colors.yellow("🔄 Force re-seeding...")}`);
        return false;
      }
    }
    
    console.log(`${colors.green("✅ Database is empty, proceeding with seeding")}`);
    return false;
  } catch (error) {
    console.log(`${colors.yellow("⚠️  Could not check database status, proceeding with seeding")}`);
    return false;
  }
}

// Seed database with demo data
async function seedDatabase() {
  console.log(`\n${colors.blue("🗄️  Seeding database with demo data...")}\n`);
  
  const seedFiles = [
    "seed-users.sql",
    "seed-workspaces.sql", 
    "seed-projects.sql",
    "seed-tasks.sql",
    "seed-ai-data.sql"
  ];
  
  for (const seedFile of seedFiles) {
    const filePath = join("scripts", "seed", seedFile);
    
    if (existsSync(filePath)) {
      console.log(`📄 Processing ${seedFile}...`);
      try {
        await $`docker compose exec -T db psql -U postgres -d postgres -f /dev/stdin < ${filePath}`;
        console.log(`   ${colors.green("✅ Success")}`);
      } catch (error) {
        console.log(`   ${colors.yellow("⚠️  Partial success or already exists")}`);
      }
    } else {
      console.log(`   ${colors.yellow("⚠️  File not found, skipping")}`);
    }
  }
  
  console.log(`\n${colors.green("✅ Database seeding completed")}`);
}

// Seed n8n workflows
async function seedWorkflows() {
  console.log(`\n${colors.blue("🔄 Importing n8n workflows...")}\n`);
  
  try {
    // Check if n8n is ready
    let retries = 10;
    while (retries > 0) {
      try {
        await $`docker compose exec n8n curl -f http://localhost:5678/healthz`.quiet();
        break;
      } catch {
        retries--;
        if (retries === 0) {
          throw new Error("n8n is not responding");
        }
        console.log("Waiting for n8n to be ready...");
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    // Import workflows and credentials
    console.log("📥 Importing credentials...");
    await $`docker compose exec n8n n8n import:credentials --separate --input=/demo-data/credentials`;
    
    console.log("📥 Importing workflows...");
    await $`docker compose exec n8n n8n import:workflow --separate --input=/demo-data/workflows`;
    
    console.log("⚡ Activating workflows...");
    await $`docker compose exec n8n n8n update:workflow --all --active=true`;
    
    console.log(`${colors.green("✅ n8n workflows imported and activated")}`);
    
  } catch (error) {
    console.log(`${colors.yellow("⚠️  n8n workflow import partially failed:")}`);
    console.log(`   This is normal if workflows already exist`);
  }
}

// Display demo credentials
async function showDemoCredentials() {
  console.log(`\n${colors.cyan("🔑 Demo Login Credentials:")}`);
  console.log(`• ${colors.green("<EMAIL>")}     - Password: ${colors.yellow("demo123!")}`);
  console.log(`• ${colors.green("<EMAIL>")}   - Password: ${colors.yellow("demo123!")}`);
  console.log(`• ${colors.green("<EMAIL>")} - Password: ${colors.yellow("demo123!")}`);
  
  console.log(`\n${colors.cyan("🌐 Access Points:")}`);
  console.log(`• Dashboard:     ${colors.blue("http://localhost:3000")}`);
  console.log(`• n8n Workflows: ${colors.blue("http://localhost:5678")}`);
  console.log(`  Username: ${colors.green("<EMAIL>")}`);
  console.log(`  Password: ${colors.yellow("pulse123!")}`);
}

// Main seeding flow
async function main() {
  try {
    await checkServices();
    
    const isAlreadySeeded = await checkDatabaseSeeded();
    
    if (!isAlreadySeeded) {
      await seedDatabase();
      await seedWorkflows();
      
      console.log(`\n${colors.green("🎉 Seeding completed successfully!")}`);
    } else {
      console.log(`\n${colors.green("✅ Database already seeded")}`);
    }
    
    await showDemoCredentials();
    
  } catch (error) {
    console.log(`\n${colors.red("❌ Seeding failed:")}`);
    console.log(error.message);
    process.exit(1);
  }
}

main();