# syntax=docker/dockerfile:1

ARG BUN_VERSION=1.2.14

FROM oven/bun:${BUN_VERSION}-alpine

# Set working directory
WORKDIR /app

# Create non-root user early (before any file operations)
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 --ingroup nodejs nextjs

# Copy package files and scripts for postinstall
COPY package.json bun.lock* ./
COPY scripts/ ./scripts/

# Install dependencies with cache mount (skip postinstall temporarily)
RUN --mount=type=cache,target=/root/.bun/install/cache \
    SKIP_POSTINSTALL=1 bun install

# Copy and set up the entrypoint script first
COPY --chmod=755 docker-entrypoint.sh /docker-entrypoint.sh

# Copy source code (excluding files in .dockerignore)
COPY . .

# Create flowbite and next directories with proper permissions
RUN mkdir -p /app/.flowbite-react && \
    echo "{}" > /app/.flowbite-react/class-list.json && \
    chmod 755 /app/.flowbite-react && \
    chmod 644 /app/.flowbite-react/class-list.json && \
    mkdir -p /app/.next && \
    chmod 755 /app/.next

# Install curl for health checks
RUN apk add --no-cache curl

# Change ownership to nextjs user
RUN chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Run postinstall script after directory setup
RUN bun run postinstall

# Expose port
EXPOSE 3000

# Set environment variables for development
ENV NODE_ENV=development \
    NEXT_TELEMETRY_DISABLED=1 \
    WATCHPACK_POLLING=true \
    CHOKIDAR_USEPOLLING=true \
    CHOKIDAR_INTERVAL=1000 \
    NEXT_DEV_HOST=0.0.0.0 \
    PATH="/app/node_modules/.bin:$PATH"

# Create health check endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Use the entrypoint script to start the development server
ENTRYPOINT ["/bin/sh", "/docker-entrypoint.sh"]
