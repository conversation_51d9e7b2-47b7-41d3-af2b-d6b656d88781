{"name": "pulse-project-management-dashboard", "version": "1.3.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "format": "prettier . --write", "format:check": "prettier . --check", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "postinstall": "node scripts/postinstall.js", "flowbite:patch": "flowbite-react patch"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "flowbite-react": "^0.11.7", "next": "15.3.3", "react": "19.1.0", "react-apexcharts": "1.4.1", "react-dom": "19.1.0", "react-icons": "^4.12.0", "react-sortablejs": "^6.1.4", "sharp": "^0.34.2", "svgmap": "^2.12.2", "tailwind-merge": "^3.3.0", "swr": "^2.2.4", "date-fns": "^3.2.0", "react-hook-form": "^7.49.2", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.4", "@next/eslint-plugin-next": "15.3.3", "@tailwindcss/postcss": "^4.1.7", "@types/react": "19.1.6", "@types/react-dom": "19.1.6", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-tailwindcss": "^3.18.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.7", "typescript": "^5.8.3"}, "packageManager": "bun@1.2.14"}